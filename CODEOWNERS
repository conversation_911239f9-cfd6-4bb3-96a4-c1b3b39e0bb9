
[Core Packages]
packages/platform/         @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture
packages/xc/               @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture
packages/zia/              @zscaler/xc/ui/maintainers/zia-leads @zscaler/xc/ui/maintainers/xc-leads
packages/adaptive-access/  @zscaler/xc/ui/maintainers/aae-leads @zscaler/xc/ui/maintainers/xc-leads


[Deprecated Packages]
packages/deprecated             @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture
packages/xc/legacy-components/  @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture

[Core Configuration]
/package.json                  @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture
CODEOWNERS                     @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture
packages/xc/app/.eslintrc.json @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture
packages/xc/app/tsconfig.json  @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture
pnpm-workspace.yaml            @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture

[Configuration]
packages/xc/app/package.json        @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture
packages/xc/app/next.config.mjs     @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture
packages/xc/app/package.json        @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture
packages/xc/app/tailwind.config.ts  @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture @zscaler/xc/ui/maintainers/xc-nimbus


[New Navigation]
packages/platform/navigation    @mlussier @zscaler/xc/ui/maintainers/xc-architecture


[Testing]
playwright-ts-test/   @zscaler/xc/ui/maintainers/xc-test @zscaler/xc/ui/maintainers/xc-architecture

[Translation]
packages/xc/app/configs/locales/   @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture
packages/xc/app/public/locales/    @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture

[Dev Flags]
packages/xc/app/modules/devFlags/*   @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture

[MultiCloud]
packages/xc/app/configs/data/cloud_simulation.ts   @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture
packages/xc/app/scripts/                           @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture


[Core Context]
packages/xc/app/context/notification    @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture
packages/xc/app/context/job             @zscaler/xc/ui/maintainers/xc-leads @zscaler/xc/ui/maintainers/xc-architecture

[ZMS Packages]
packages/zms    @xli @fsheiness @zscaler/xc/ui/maintainers/xc-architecture

[CI]
.gitlab-ci.yml @cthimmareddygari @kdev @pkeenan @mlussier @plin
.gitlab/ci/ @cthimmareddygari @kdev @pkeenan @mlussier @plin
