# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
# build
.gitdescribe
node_modules/*
dist/*

# husky
.husky

# Snyk
.snyk

# dependencies
/node_modules
/.pnp
.pnp.js
storybook-static
/src/__snapshots__
/dist

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# artifacts
**/*.rpm
**/*.tar.gz

package-lock.json
.eslintcache
packages/eslint-plugin-zuxp/node_modules
test-automation/test-results
test-automation/playwright-report
test-automation/node_modules
test-automation/.env
test-automation/screenshots
test-automation/playwright/.auth
tools/translations/zl10ncli
packages/stores/node_modules

# vscode settings
.vscode/launch.json
tools/translations/translation
packages/xc/app/mockery.log
packages/webpack-copy-plugin/dist
packages/webpack-copy-plugin/node_modules
packages/feature-flags/dist
packages/feature-flags/node_modules

packages/ma/core/bun_oneui_pages_dist

# other
**/dist/
**/node_modules/
packages/xc/tools/translation/zl10ncli

/docker/service/data

*storybook.log
token.mk
packages/xc/app/test-output
packages/xc/tools/cli/tsconfig.tsbuildinfo

# @zpa/pages
packages/zpa/pages/test-output
packages/zpa/pages/coverage
