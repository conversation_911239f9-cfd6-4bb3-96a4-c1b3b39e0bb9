import { usePathname } from "next/navigation";
import { useTranslation } from "react-i18next";
import { Card } from "@zs-nimbus/core";
import Sankey from "../../../Charts/Sankey/Sankey";
import { type SandboxThreatsProps } from "./types";
import ZButtonLink from "@/components/ZLink/ZButtonLink";
import { getDataTestId } from "@/utils/utils";

const SandboxThreats = ({ chartData, cardConfig, id }: SandboxThreatsProps) => {
  const baseURL = usePathname();
  const { t } = useTranslation();

  return (
    <Card.Root data-testid={getDataTestId(`card-container`, id)}>
      <Card.Header>
        <span
          data-testid={getDataTestId(`card-title`, id)}
          className="text-semantic-content-base-primary typography-header5"
        >
          {t(cardConfig.cardTitle ?? "")}
        </span>
        <div
          className="text-semantic-content-base-secondary typography-paragraph2"
          data-testid={getDataTestId(`card-description`, id)}
        >
          {t(cardConfig.cardDescription ?? "")}
        </div>
      </Card.Header>

      <Card.Body data-testid={getDataTestId(`card-content`, id)}>
        <Sankey {...chartData} chartId={id} />
      </Card.Body>
      <Card.Footer className="flex justify-end">
        <ZButtonLink
          type="Breadcrumb"
          href={`${baseURL}/sandbox-threat`}
          id={getDataTestId(`footer-navLink`, id)}
        >
          {t(cardConfig.cardFooterTitle ?? "")} {">"}
        </ZButtonLink>
      </Card.Footer>
    </Card.Root>
  );
};

export { SandboxThreats };
