import { usePathname } from "next/navigation";
import { useTranslation } from "react-i18next";
import { Card } from "@zs-nimbus/core";
import { type YourSSLInspectionReviewProps } from "./types";
import BarLineChart from "@/components/Analytics/Charts/BarLineChart/BarLineChart";
import ZButtonLink from "@/components/ZLink/ZButtonLink";
import { getDataTestId } from "@/utils/utils";

const YourSSLInspectionReview = ({
  chartData,
  id,
}: YourSSLInspectionReviewProps) => {
  const baseURL = usePathname();
  const { t } = useTranslation();

  return (
    <Card.Root data-testid={getDataTestId(`card-container`, id)}>
      <Card.Header>
        <span
          className="text-semantic-content-base-primary typography-header5"
          data-testid={getDataTestId(`card-title`, id)}
        >
          {t("YOUR_SSL_INSPECTION")}
        </span>
        <div
          className="text-semantic-content-base-secondary typography-paragraph2"
          data-testid={getDataTestId(`card-description`, id)}
        >
          {t("SOPHISTICATED_THREATS")}
        </div>
      </Card.Header>

      <Card.Body data-testid={getDataTestId(`card-content`, id)}>
        <BarLineChart {...chartData} chartId={id} />
      </Card.Body>
      <Card.Footer className="flex justify-end">
        <ZButtonLink
          href={`${baseURL}/ssl-inspection`}
          id={getDataTestId(`footer-navLink`, id)}
          type="Breadcrumb"
        >
          {t("VIEW_SSL_INSPECTION")} {">"}
        </ZButtonLink>
      </Card.Footer>
    </Card.Root>
  );
};

export { YourSSLInspectionReview };
