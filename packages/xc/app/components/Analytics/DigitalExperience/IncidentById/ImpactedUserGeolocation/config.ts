import { t } from "i18next";
import { colors } from "@zs-nimbus/foundations";
import { type BubbleMapConfig } from "@/components/Analytics/Charts/WorldMapWithBubbles/types";
import { type ThemeTypes } from "@/context/UserPreferenceContext";

export const getImpactedUserGeoConfig = (theme: ThemeTypes) => ({
  heading: t("IMPACTED_USERS_BY_GEOLOCATIONS"),
  customConfig: {
    bubbleMapProps: {
      containerClass: "h-[348px]",
      mapProps: {
        mapBackgroundColor: colors[theme].surface.fields.disabled,
        mapStrokeColor: colors[theme].border.base.primary,
        mapRegionColor: colors[theme].surface.fields.default,
      },
      defaultZoomSetting: {
        geoPoint: { latitude: 23.6345, longitude: -102.5528 },
        level: 2.5,
      },
      tooltipProps: {
        tooltipBackgroundSetting: {
          fillColor: colors[theme].surface.elevated.low10,
        },
      },
    },
  } as BubbleMapConfig,
  footerText: t("IMPACTED_USERS_BY_GEOLOCATIONS_FOOTER"),
});
