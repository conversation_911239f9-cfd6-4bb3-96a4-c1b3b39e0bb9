import Link from "next/link";
import { t } from "i18next";
import { type IncidentTableRowItemProps, type RenderMap } from "./types";
import { ColumnItemWrapper } from "@/components/Analytics/DataTable/ColumnItemWrapper";
import { formatDurationMinutes } from "@/utils/utils";
import { formatTimestamp } from "@/components/Analytics/DataTable/utils";

export const columnRenderers: RenderMap = {
  inc_type: (item) => (
    <ColumnItemWrapper customClass="text-semantic-brand-default">
      <Link
        href={`incidents/details?id=${encodeURI(item.inc_id)}&epicenter=${encodeURI(item.inc_epicenter_city ?? "")}&type=${encodeURI(item.inc_type ?? "")}`}
      >
        {t(
          `${item?.inc_type?.split(" ").join("_").toUpperCase()}_TABLE_COLUMN`,
        ) ??
          item?.inc_type ??
          ""}
      </Link>
    </ColumnItemWrapper>
  ),
  inc_epicenter_city: (item) => (
    <ColumnItemWrapper>
      <span className="overflow-hidden text-ellipsis whitespace-nowrap">
        {`${item?.inc_epicenter_city}${item?.inc_attr ? `, ${item?.inc_attr}` : ""}`}
      </span>
    </ColumnItemWrapper>
  ),
  inc_impacted_user_count: (item) => (
    <ColumnItemWrapper>
      <span className="overflow-hidden text-ellipsis whitespace-nowrap">
        {item?.inc_impacted_user_count}
      </span>
    </ColumnItemWrapper>
  ),
  incident_duration_mins: (item) => (
    <ColumnItemWrapper>
      <span className="overflow-hidden text-ellipsis whitespace-nowrap">
        {formatDurationMinutes(item.inc_etime, item.inc_stime)}
      </span>
    </ColumnItemWrapper>
  ),
  inc_stime: (item, _, timeZoneAbbr) => (
    <ColumnItemWrapper>
      <span className="overflow-hidden text-ellipsis whitespace-nowrap">
        {formatTimestamp(Number(item?.inc_stime), true) + timeZoneAbbr}
      </span>
    </ColumnItemWrapper>
  ),
  inc_etime: (item, _, timeZoneAbbr) => (
    <ColumnItemWrapper>
      <span className="overflow-hidden text-ellipsis whitespace-nowrap">
        {formatTimestamp(Number(item?.inc_etime), true) + timeZoneAbbr}
      </span>
    </ColumnItemWrapper>
  ),
  default: (item, column) => (
    <ColumnItemWrapper>
      {item[column.id as keyof IncidentTableRowItemProps]}
    </ColumnItemWrapper>
  ),
};
