import Link from "next/link";
import { useState } from "react";
import { t } from "i18next";
import { useUserInfo } from "@/context/UserInfoContext";
import { handleLogout } from "@/utils/auth/handleLogout";
import { getDataTestId } from "@/utils/utils";

type ErrorProps = {
  text: string;
  id?: string;
};

export type OptionProps = {
  icon: string;
  key?: string;
  text: string;
  href?: string;
};

type AccountPopoverProps = {
  options: OptionProps[];
};

export const Loader = ({ id }: { id?: string }) => (
  <div
    className="bg-grey-100 rounded-m p-xs relative overflow-hidden w-full h-full z-[60]"
    data-testid={getDataTestId("popper-loader", id)}
  >
    <div className="absolute inset-none bg-gradient-to-r from-grey-100 to-grey-200 rounded-m animate-pulse" />
    <div className="flex flex-col space-y-2">
      <div className="h-m bg-grey-100 rounded-40" />
    </div>
  </div>
);

export const ErrorState = ({ text, id }: ErrorProps) => (
  <div
    className="flex text-semantic-content-base-tertiary w-full h-full"
    data-testid={getDataTestId("popper-error-state", id)}
  >
    <span className="typography-paragraph2">{text}</span>
  </div>
);

function OnboardingAccountPopover({ options }: AccountPopoverProps) {
  const ID = "onboarding-account-popover";
  const { userInfo } = useUserInfo();

  const [signoutDisabled, setSignoutDisabled] = useState<boolean>(false);

  const loginID = () => {
    if (userInfo?.loginName) return userInfo?.loginName;
    if (userInfo?.loading) return <Loader id="login" />;
    if (userInfo?.error)
      return <ErrorState text="Something went wrong" id="login" />;
  };

  const organization = () => {
    if (userInfo?.organization?.name) return userInfo?.organization?.name;
    if (userInfo?.organization?.loading) return <Loader id="organization" />;
    if (userInfo?.organization?.error)
      return <ErrorState text={"Something went wrong"} id="organization" />;
  };

  return (
    <div
      className="absolute w-[18rem] shadow-elevation-3 rounded-40 mt-rem-160 right-none bg-semantic-surface-base-primary text-semantic-content-base-primary z-50"
      data-testid={ID}
    >
      <span className="absolute transform rotate-45 top-[-5px] right-xxl border-8 z-50 border-semantic-border-inverted-primary" />
      <div className="flex flex-col border-b-[1px] border-b-semantic-border-base-primary py-rem-160 px-rem-120 gap-rem-160 text-semantic-content-base-primary break-words">
        <div className="my-xxs">
          <div
            className="typography-paragraph2-strong text-semantic-content-base-tertiary"
            data-testid={getDataTestId("key-1", ID)}
          >
            {t("OB_ACCOUNT_LOGIN_ID")}
          </div>
          <div
            className="typography-paragraph1 text-semantic-content-base-primary"
            data-testid={getDataTestId("value-1", ID)}
          >
            {loginID()}
          </div>
        </div>

        <div className="my-xxs">
          <div
            className="typography-paragraph2-strong text-semantic-content-base-tertiary"
            data-testid={getDataTestId("key-2", ID)}
          >
            {t("OB_ACCOUNT_ORGANIZATION")}
          </div>
          <div
            className="typography-paragraph1 text-semantic-content-base-primary"
            data-testid={getDataTestId("value-2", ID)}
          >
            {organization()}
          </div>
        </div>
      </div>

      <ul className="text-semantic-content-interactive-primary-default flex flex-col gap-l my-rem-160">
        {options?.map((option, index) =>
          option.key !== "sign-out" ? (
            <Link
              key={index}
              className="px-rem-120 flex items-center space-x-default cursor-pointer focus-visible-default"
              href={option.href ?? ""}
              data-testid={getDataTestId(`sign-out-${index}`, ID)}
            >
              <i
                aria-label={t("OAP_ICON", { text: option?.text })}
                className={option.icon}
              />
              <span
                className="typography-paragraph1"
                data-testid={getDataTestId(`text-${index}`, ID)}
              >
                {option.text}
              </span>
            </Link>
          ) : (
            <button
              key={index}
              data-testid={getDataTestId(`sign-out-${index}`, ID)}
              className="px-rem-120 flex items-center space-x-default gap-default cursor-pointer focus-visible-default"
              type="button"
              onClick={() => {
                if (!signoutDisabled) {
                  setSignoutDisabled(true);
                  void handleLogout();
                }
              }}
            >
              <i
                aria-label={t("OAP_ICON", { text: option?.text })}
                className={` ${option.icon}`}
              />
              <span
                className="typography-paragraph1"
                data-testid={getDataTestId(`text-${index}`, ID)}
              >
                {t(option.text)}
              </span>
            </button>
          ),
        )}
      </ul>
    </div>
  );
}

export default OnboardingAccountPopover;
