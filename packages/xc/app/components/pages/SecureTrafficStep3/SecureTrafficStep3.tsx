import { ZDataTable, Zselect } from "@xc/legacy-components";
import debounce from "lodash/debounce";
import { useState } from "react";
import { t } from "i18next";
import { useTranslation } from "react-i18next";
import { Button } from "@zs-nimbus/core";
import { faRotateLeft } from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { type UserRoleFilterOptionsProps } from "../HostedUsers/types";
import OnboardingFooter, {
  type OnboardingFooterProps,
} from "@/components/OnboardingLayout/OnboardingFooter";
import SearchBar from "@/components/SearchBar/SearchBar";
import Stepper, { type StepperProps } from "@/components/Stepper/Stepper";
import { OnboardingContent } from "@/components/OnboardingLayout/OnboardingContent";
import OnboardingWrapper from "@/components/OnboardingLayout/OnboardingWrapper";
import { type TransformedResponse } from "@/app/onboarding/setup-users/hosted-users/apiHelper";
import { getDataTestId, handleKeyboardDown } from "@/utils/utils";
import TableLoader from "@/components/TableLoader/TableLoader";

type HeaderProps = {
  id?: string;
  headline: string;
  text: string;
};

type TableRenderItem = {
  columnIndex: number;
  rowIndex: number;
  item: TableRowItemProps;
  column: TableColumnItemProps;
};

type TableColumnItemProps = {
  id: string;
  name: string;
  isSortable: boolean;
  isHidden: boolean;
  renderItem?: (item: TableRenderItem) => void;
  width: string;
};

type TableFilterItemProps = {
  id: string;
  name: string;
};

export type TableRowItemProps = {
  name: string;
  email: string;
  loginID?: string;
  id?: string;
  role: string;
};

type TablePaginationProps = {
  enabled: boolean;
  pageSize: number;
  currentPage: number;
  allowedPageSizes: number[];
  hideSummary: boolean;
};

type TableProps = {
  tableColumnFn: () => TableColumnItemProps[];
  tablePaginationData?: TablePaginationProps;
  tableFilterData: TableFilterItemProps[];
  fetchRows: (
    currentPage: number,
    pageSize: number,
  ) => Promise<TransformedResponse>;
  tableRef: React.Ref<{ refreshData: () => void }>;
  onRowSelect: (row: TableRowItemProps) => void;
  onRowUnSelect: (row: TableRowItemProps) => void;
};

type SecureTrafficStep3ContentProps = TableProps &
  HeaderProps & {
    id?: string;
    onCollapseExpand?: (value: boolean) => void;
    isDropdownExpanded?: boolean;
    resetText: string;
    onUserRoleFilterChange: (filters: UserRoleFilterOptionsProps[]) => void;
    onSearchChange: (val: string) => void;
  };

export type SecureTrafficStep3Props = SecureTrafficStep3ContentProps & {
  stepper: StepperProps;
  footer: OnboardingFooterProps;
};

function SecureTrafficStep3Content({
  id,
  headline,
  text,
  tableColumnFn,
  tableFilterData,
  tablePaginationData,
  onRowSelect,
  onRowUnSelect,
  onCollapseExpand,
  isDropdownExpanded,
  resetText,
  fetchRows,
  tableRef,
  onUserRoleFilterChange,
  onSearchChange,
}: SecureTrafficStep3ContentProps) {
  const [userRoleFilters, setUserRoleFilters] = useState<
    UserRoleFilterOptionsProps[]
  >([]);

  const { t } = useTranslation();

  const handleSearchResult = debounce((value: string) => {
    onSearchChange(value);
  }, 300);

  const handleUserRoleFilter = (options: UserRoleFilterOptionsProps[]) => {
    onUserRoleFilterChange(options);
    setUserRoleFilters(options);
  };

  const columnData = tableColumnFn();

  const translatedTableColumnData = columnData.map((tcd) => {
    tcd.name = t(tcd.name);

    return tcd;
  });

  return (
    <div>
      <Header headline={headline} text={text} id={id} />
      <div className="flex justify-between items-center mb-m">
        <div className="flex gap-m items-center">
          <Zselect
            id={id}
            showSelectedOptions={false}
            idAttr="id"
            valueAttr="name"
            multiSelect
            enableSelectAll={false}
            flip={false}
            enableCancel={false}
            options={tableFilterData}
            onExpandCollapse={onCollapseExpand}
            wrapperCustomClass="relative"
            enableClearSelection={false}
            showClearSelection={false}
            isPill
            searchOptions={{ enable: false }}
            customClass="fix-padding-initial-render typography-paragraph1 !font-normal text-semantic-content-base-primary pill"
            onSelectionChange={handleUserRoleFilter}
            preSelectedOptions={userRoleFilters}
            overrideCollapsedView={(
              ...props: [boolean, UserRoleFilterOptionsProps[]]
            ) => {
              const show = props?.[0];
              const selectedOption = props?.[1];

              const summaryText = selectedOption
                .map((elem) => elem.name)
                .join(", ");

              return (
                <>
                  <span className={`${!show && "summary-text"}`}>
                    {!!summaryText.length ? `${t("ROLE")} = ` : t("ROLE")}
                  </span>
                  {!!summaryText.length && (
                    <span className={`${!show && "text-selected"}`}>
                      {summaryText}
                    </span>
                  )}
                </>
              );
            }}
            onActiveShowValue={false}
            showDropdownIconVariation={false}
          />
          {!isDropdownExpanded && userRoleFilters.length > 0 && (
            <Button
              data-testid={getDataTestId("filter-reset-btn", id)}
              variant="tertiary"
              prefixIcon={<FontAwesomeIcon icon={faRotateLeft} />}
              onClick={() => handleUserRoleFilter([])}
              onKeyDown={handleKeyboardDown(() => handleUserRoleFilter([]))}
              tabIndex={0}
            >
              {t(resetText)}
            </Button>
          )}
        </div>
        <div className="flex">
          <SearchBar onChange={handleSearchResult} isOnlyExpanded id={id} />
        </div>
      </div>

      <div className="flex max-h-[412px] overflow-hidden">
        <ZDataTable
          id={id}
          columns={translatedTableColumnData}
          noBulkSelection
          onRowSelect={onRowSelect}
          onRowUnSelect={onRowUnSelect}
          pagination={tablePaginationData}
          fetchRows={fetchRows}
          ref={tableRef}
          selectedClass=""
          loadingComponent={<TableLoader rows={7} />}
        />
      </div>
    </div>
  );
}

function Header({ id, headline, text }: HeaderProps) {
  return (
    <div className="flex flex-col gap-rem-160 mt-rem-160 mb-m">
      <div
        className=" typography-header4 text-semantic-content-base-primary"
        data-testid={getDataTestId("heading", id)}
      >
        {t(headline)}
      </div>

      <div
        className="typography-paragraph1 text-semantic-content-base-primary"
        data-testid={getDataTestId("description", id)}
      >
        <span>{t(text)}</span>
      </div>
    </div>
  );
}

export default function SecureTrafficStep3({
  stepper: { max, value },
  headline,
  text,
  tableColumnFn,
  tableFilterData,
  footer,
  onRowSelect,
  onRowUnSelect,
  tablePaginationData,
  onCollapseExpand,
  isDropdownExpanded,
  resetText,
  fetchRows,
  tableRef,
  onUserRoleFilterChange,
  onSearchChange,
}: SecureTrafficStep3Props) {
  const { t } = useTranslation();
  const ID = "secure-traffic-step-3";

  return (
    <OnboardingWrapper>
      <OnboardingContent id={ID}>
        <div className="flex flex-col py-l bg-semantic-surface-base-primary">
          <Stepper max={max} value={value} id={ID} />
          <SecureTrafficStep3Content
            id={ID}
            headline={t(headline)}
            text={t(text)}
            tableColumnFn={tableColumnFn}
            tableFilterData={tableFilterData}
            onRowSelect={onRowSelect}
            onRowUnSelect={onRowUnSelect}
            tablePaginationData={tablePaginationData}
            isDropdownExpanded={isDropdownExpanded}
            onCollapseExpand={onCollapseExpand}
            resetText={t(resetText)}
            fetchRows={fetchRows}
            tableRef={tableRef}
            onUserRoleFilterChange={onUserRoleFilterChange}
            onSearchChange={onSearchChange}
          />
        </div>
      </OnboardingContent>
      <OnboardingFooter {...footer} />
    </OnboardingWrapper>
  );
}
