import classNames from "classnames";
import { useTranslation } from "react-i18next";
import { getDataTestId } from "@/utils/utils";
import { useFlags } from "@/context/FeatureFlags";

export type StepProps = {
  id?: string;
  heading: string;
  children: (props?: {
    id?: string;
    onSkip: () => void;
    hasSkip?: boolean;
  }) => React.ReactNode;
  /** Expects a font awesome icon class pair.
   *
   * @example "fa fa-plus" */
  iconClasses: string;
  theme?: "default" | "active" | "partial" | "complete" | "skipped";
  line?: "green" | "default" | "hidden" | "grey";
  onSkip?: () => void;
};

const COLORS = {
  active:
    "bg-semantic-surface-interactive-primary-default text-semantic-background-primary",
  partial:
    "bg-semantic-surface-nav-sideBar-active text-semantic-content-base-primary",
  complete:
    "bg-semantic-content-status-success-secondary text-semantic-background-primary",
  skipped: "text-semantic-content-status-neutral-secondary",
  default: "text-semantic-surface-base-brand",
};

export default function Step({
  id,
  children,
  heading,
  iconClasses,
  theme = "default",
  line = "default",
  onSkip,
}: StepProps) {
  const { t } = useTranslation();

  const handleSkip = () => {
    onSkip?.();
  };
  const { can } = useFlags();

  return (
    <>
      <div className="flex items-center gap-[5px] pr-[5px]">
        <div
          data-testid={getDataTestId("icon", id)}
          className={classNames(
            "w-[40px] h-[40px] rounded-xxxl flex items-center justify-center",
            COLORS[theme],
          )}
        >
          <i aria-label={t("STEP_ICON", { heading })} className={iconClasses} />
        </div>
        {line !== "hidden" && (
          <hr
            className={`flex flex-grow ${line === "green" ? "border border-semantic-border-status-success-active" : line === "grey" ? "border-semantic-border-interactive-primary-disabled" : "border-semantic-border-interactive-primary-default"}`}
          />
        )}
      </div>
      <h2
        className="typography-header5 text-semantic-content-base-primary my-rem-160"
        data-testid={getDataTestId("heading", id)}
      >
        {t(heading)}
      </h2>
      <div className="w-[240px] h-[60px]">
        {children({
          onSkip: handleSkip,
          hasSkip: can("showOnboardingSkip"),
          id: id,
        })}
      </div>
    </>
  );
}
