import { t } from "i18next";
import { <PERSON><PERSON>, <PERSON> } from "@zs-nimbus/core";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCircleInfo } from "@fortawesome/pro-solid-svg-icons";
import { type ThreeStepProps } from "./ThreeSteps";
import { type StepProps } from "./Step";
import ZButtonLink from "@/components/ZLink/ZButtonLink";
import ONBOARDING_PATHS from "@/configs/locales/OnboardingPaths";
import {
  PARTIAL,
  INCOMPLETE,
  COMPLETE,
  ACTIVE,
  SKIPPED,
} from "@/configs/constants";
import { type Statuses } from "@/utils/onboarding";
import { getDataTestId } from "@/utils/utils";

export const STAGE = [
  "default",
  "user-data",
  "traffic-data",
  "policy-partial",
] as const;

export function EditIconButton({
  href,
  header,
  id,
}: {
  href: string;
  header: string;
  id?: string;
}) {
  return (
    <div className="pt-rem-200">
      <ZButtonLink
        type="tertiary"
        href={href}
        data-testid={getDataTestId("edit", id)}
      >
        <i
          aria-label={t("OB_TS_EDIT", { header: header })}
          className="fa-regular fa-pen text-semantic-brand-default"
        />
        <span className="pl-rem-80">{t("EDIT")}</span>
      </ZButtonLink>
    </div>
  );
}

type Stage = (typeof STAGE)[number];

export function stage(value: unknown): Stage {
  const _stage = value as Stage;

  return STAGE.includes(_stage) ? _stage : "default";
}

const getSkippedData = ({
  buttonLabel,
  href,
  id,
}: {
  buttonLabel: string;
  href: string;
  id?: string;
}) => (
  <>
    <Chip
      data-testid={getDataTestId("skipped-chip", id)}
      color="neutral"
      label="Not Configured"
      icon={<FontAwesomeIcon icon={faCircleInfo} />}
    />
    <div className="pt-rem-160">
      <ZButtonLink
        type="tertiary"
        href={href}
        data-testid={getDataTestId("nav-link", id)}
      >
        {t(buttonLabel)}
      </ZButtonLink>
    </div>
  </>
);

const getActiveData = ({
  heading,
  buttonLabel,
  props,
  href,
  id,
}: {
  heading: string;
  buttonLabel: string;
  props: { onSkip: () => void; hasSkip?: boolean };
  href: string;
  id?: string;
}) => (
  <>
    <p
      className="typography-paragraph1 text-semantic-content-base-primary"
      data-testid={getDataTestId("description", id)}
    >
      {t(heading)}
    </p>
    <div className="pt-rem-160 flex gap-rem-40">
      <ZButtonLink
        type="primary"
        href={href}
        data-testid={getDataTestId("button", id)}
      >
        {t(buttonLabel)}
      </ZButtonLink>
      {props.hasSkip && (
        <Button
          variant="tertiary"
          onClick={props?.onSkip}
          data-testid={getDataTestId("skip", id)}
        >
          {t("SKIP")}
        </Button>
      )}
    </div>
  </>
);

const getCompletedData = ({
  title,
  count,
  href,
  id,
}: {
  title: string;
  count: number | string;
  href: string;
  id?: string;
}) => (
  <>
    <p
      className="typography-header5 text-semantic-content-base-primary"
      data-testid={getDataTestId("sub-heading", id)}
    >
      {t(title)}
    </p>
    <p
      className="typography-header3 text-semantic-content-base-primary py-rem-40"
      data-testid={getDataTestId("count", id)}
    >
      {count}
    </p>
    <EditIconButton href={href} header={title} id={id} />
  </>
);

export const THREE_STEPS_DEFAULT_DATA: ThreeStepProps = {
  text: {
    header: {
      heading: "OB_TS_DEFAULT_HEADER_HEADING",
      subheading: "OB_TS_DEFAULT_HEADER_SUBHEADING",
    },
    completeCard: {
      heading: "OB_TS_DEFAULT_CC_HEADING",
      subheading: "OB_TS_DEFAULT_CC_SUBHEADING",
    },
  },
  steps: [
    {
      heading: "OB_TS_STEPS_SU_HEADING",
      children: (props) =>
        getActiveData({
          href: ONBOARDING_PATHS.users,
          heading: "OB_TS_STEPS_SU_META",
          buttonLabel: "OB_TS_STEPS_SU_BTN",
          props: props!,
          id: props?.id,
        }),
      iconClasses: "fa-regular fa-user-group text-[18px]",
      theme: ACTIVE,
    },
    {
      heading: "OB_TS_STEPS_ST_HEADING",
      children: (props) => (
        <p
          className="typography-paragraph1 text-semantic-content-base-secondary"
          data-testid={getDataTestId("description", props?.id)}
        >
          {t("OB_TS_STEPS_ST_META")}
        </p>
      ),
      iconClasses: "fal fa-globe text-[28px]",
    },
    {
      heading: "OB_TS_STEPS_SP_HEADING",
      children: (props) => (
        <p
          className="typography-paragraph1 text-semantic-content-base-secondary"
          data-testid={getDataTestId("description", props?.id)}
        >
          {t("OB_TS_STEPS_SP_META")}
        </p>
      ),
      iconClasses: "fa-light fa-monitor-waveform text-[26px]",
      line: "hidden",
    },
  ],
  stage: INCOMPLETE,
  loading: false,
};

type UsersThreeStepsDataProps = {
  status: Statuses;
  hasSkip: boolean;
};

export const getUsersThreeStepData = ({
  status,
}: UsersThreeStepsDataProps): ThreeStepProps => {
  const statusMap = (): Record<string, StepProps> => ({
    partial: {
      ...THREE_STEPS_DEFAULT_DATA.steps[0],
      children: (props) => (
        <>
          <p
            className="typography-header5 text-semantic-content-base-primary"
            data-testid={getDataTestId("prompt", props?.id)}
          >
            {t("OB_TS_UP_STEP_META")}
            <br />
            <EMAIL>
          </p>
          <EditIconButton
            id={props?.id}
            href={ONBOARDING_PATHS.hosted_users}
            header={"OB_TS_UP_STEP_META"}
          />
        </>
      ),
      iconClasses: "fa-solid fa-hourglass-start",
      theme: PARTIAL,
    },
    skipped: {
      ...THREE_STEPS_DEFAULT_DATA.steps[0],
      children: (props) =>
        getSkippedData({
          href: ONBOARDING_PATHS.users,
          buttonLabel: "OB_TS_STEPS_SU_BTN",
          id: props?.id,
        }),
      theme: SKIPPED,
      line: "grey",
    },
    complete: {
      ...THREE_STEPS_DEFAULT_DATA.steps[0],
      children: (props) =>
        getCompletedData({
          title: "OB_TS_CONFIGURED_USERS",
          count: 6,
          href: ONBOARDING_PATHS.hosted_users,
          id: props?.id,
        }),
      theme: COMPLETE,
      line: "green",
    },
  });

  return {
    ...THREE_STEPS_DEFAULT_DATA,
    steps: [
      statusMap()[status!],
      {
        ...THREE_STEPS_DEFAULT_DATA.steps[1],
        children: (props) =>
          getActiveData({
            heading: "OB_TS_STEPS_ST_META",
            buttonLabel: "OB_TS_STEPS_ST_HEADING",
            href: ONBOARDING_PATHS.secure_traffic,
            props: props!,
            id: props?.id,
          }),
        theme: ACTIVE,
      },
      THREE_STEPS_DEFAULT_DATA.steps[2],
    ],
  };
};

type TrafficThreeStepsDataProps = {
  usersStatus: Statuses;
  status: Statuses;
  hasSkip: boolean;
};

export const getTrafficThreeStepData = ({
  usersStatus,
  status,
  hasSkip,
}: TrafficThreeStepsDataProps): ThreeStepProps => {
  const statusMap = (): Record<string, StepProps> => ({
    skipped: {
      ...THREE_STEPS_DEFAULT_DATA.steps[1],
      children: (props) =>
        getSkippedData({
          href: ONBOARDING_PATHS.secure_traffic,
          buttonLabel: "OB_TS_STEPS_ST_HEADING",
          id: props?.id,
        }),
      theme: SKIPPED,
      line: "grey",
    },
    complete: {
      ...THREE_STEPS_DEFAULT_DATA.steps[1],
      children: (props) =>
        getCompletedData({
          title: "OB_TS_CONNECTED_USERS",
          count: 1,
          href: ONBOARDING_PATHS.secure_traffic,
          id: props?.id,
        }),
      theme: COMPLETE,
    },
  });

  return {
    ...THREE_STEPS_DEFAULT_DATA,
    steps: [
      getUsersThreeStepData({ status: usersStatus, hasSkip }).steps[0],
      statusMap()[status!],
      {
        ...THREE_STEPS_DEFAULT_DATA.steps[2],
        children: (props) =>
          getActiveData({
            heading: "OB_TS_STEPS_SP_META",
            href: ONBOARDING_PATHS.policy_url,
            buttonLabel: "OB_TS_STEPS_SP_HEADING",
            props: props!,
            id: props?.id,
          }),
        theme: ACTIVE,
      },
    ],
    stage: PARTIAL,
  };
};

type PolicyThreeStepsDataProps = {
  steps: number;
  href: string;
  userStatus: UsersThreeStepsDataProps["status"];
  trafficStatus: TrafficThreeStepsDataProps["status"];
  hasSkip: boolean;
};

export const getPartialPolicyThreeStepsData = ({
  steps,
  href,
  userStatus,
  trafficStatus,
  hasSkip,
}: PolicyThreeStepsDataProps): ThreeStepProps => ({
  ...THREE_STEPS_DEFAULT_DATA,
  steps: [
    getUsersThreeStepData({ status: userStatus, hasSkip }).steps[0],
    getTrafficThreeStepData({
      status: trafficStatus,
      usersStatus: userStatus,
      hasSkip,
    }).steps[1],
    {
      ...THREE_STEPS_DEFAULT_DATA.steps[2],
      heading: "OB_TS_STEPS_SP_HEADING",
      children: (props) =>
        getCompletedData({
          title: "OB_TS_COMPLETED_STEPS",
          count: `${steps}/7`,
          href: href,
          id: props?.id,
        }),
      theme: ACTIVE,
    },
  ],
  stage: PARTIAL,
});

export const THREE_STEPS_DATA = {
  default: THREE_STEPS_DEFAULT_DATA,
  "user-data": ({ status, hasSkip }: UsersThreeStepsDataProps) =>
    getUsersThreeStepData({
      status: status,
      hasSkip,
    }),
  "traffic-data": ({
    status,
    usersStatus,
    hasSkip,
  }: TrafficThreeStepsDataProps) =>
    getTrafficThreeStepData({
      status: status,
      usersStatus: usersStatus,
      hasSkip,
    }),
  "policy-partial": ({
    steps,
    href,
    userStatus,
    trafficStatus,
    hasSkip,
  }: PolicyThreeStepsDataProps) =>
    getPartialPolicyThreeStepsData({
      steps: steps,
      href: href,
      userStatus: userStatus,
      trafficStatus: trafficStatus,
      hasSkip,
    }),
} satisfies Record<
  Stage,
  | ThreeStepProps
  | (({ status }: UsersThreeStepsDataProps) => ThreeStepProps)
  | (({ status, usersStatus }: TrafficThreeStepsDataProps) => ThreeStepProps)
  | (({ steps, href }: PolicyThreeStepsDataProps) => ThreeStepProps)
>;
