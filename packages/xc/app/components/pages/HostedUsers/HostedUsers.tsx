/* eslint-disable @up/unified-platform/max-file-lines */
import {
  type Dispatch,
  type SetStateAction,
  type ReactElement,
  useState,
} from "react";
import debounce from "lodash/debounce";

import { Zselect, ZDataTable, ZMenu, Tooltip } from "@xc/legacy-components";
import { useTranslation } from "react-i18next";
import { Button } from "@zs-nimbus/core";
import {
  faCircleMinus,
  faFingerprint,
  faPlus,
  faUpload,
} from "@fortawesome/pro-regular-svg-icons";
import { faRotateLeft } from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import DisconnectModal from "./DisconnectModal/DisconnectModal";
import {
  type TableRenderItem,
  type TableProps,
  type TableRowItemProps,
  type MenuItem,
  type TableActionProps,
  type UserRoleFilterOptionsProps,
} from "./types";
import HostedUserLoader from "./HostedUserLoader";
import RemoveModal from "@/components/RemoveModal/RemoveModal";
import SearchBar from "@/components/SearchBar/SearchBar";
import UploadModal from "@/components/UploadModal/UploadModal";
import UserModal from "@/components/UserModal/UserModal";
import { type BlockquoteWithIconProps } from "@/components/BlockquoteWithIcon/BlockquoteWithIcon";
import OnboardingFooter, {
  type OnboardingFooterProps,
} from "@/components/OnboardingLayout/OnboardingFooter";
import { OnboardingContent } from "@/components/OnboardingLayout/OnboardingContent";
import ZButtonLink from "@/components/ZLink/ZButtonLink";
import OnboardingWrapper from "@/components/OnboardingLayout/OnboardingWrapper";
import ONBOARDING_PATHS from "@/configs/locales/OnboardingPaths";
import { type FailedRecords } from "@/app/onboarding/setup-users/hosted-users/apiHelper";
import { WithStates } from "@/hoc/WithStates";
import TableLoader from "@/components/TableLoader/TableLoader";
import { getDataTestId } from "@/utils/utils";

type AddUserButtonProps = {
  setShowUserModal: (showUserModal: boolean) => void;
};
type UserRowDataItemsTooltipProps = {
  tooltipMessage: string;
};
type UploadCSVButtonProps = {
  uploadModalOpen?: boolean;
  isUploadCSV?: boolean;
  uploadState?: "initial" | "processing" | "success" | "failed";
  onUpload?: (formdata: FormData) => void;
  setShowUploadModal?: (value: boolean) => void;
  failedRecords: FailedRecords[];
};
type UserWithIconProps = {
  selected: boolean;
  icon: string;
  id: string;
  headline: string;
  description: string;
};
type IDPConnectProps = {
  id?: string;
  connected?: boolean;
  isModalActive: boolean;
  connectedButton: string;
  idpName: string;
  disconnectedButton: string;
  setModalActive?: Dispatch<SetStateAction<boolean>>;
};

export type UserModalProps = {
  title: string;
  warningText: string;
  confirmationText: string;
  modalText: string;
  userModalOpen?: boolean;
};

type HostedUsersContentProps = IDPConnectProps &
  TableProps &
  TableActionProps &
  AddUserButtonProps &
  UploadCSVButtonProps &
  UserRowDataItemsTooltipProps & {
    id?: string;
    resetText: string;
    deleteUserText: string;
    editUserText: string;
    addUserText: string;
    uploadCSVText: string;
    editButtonText: string;
    deleteButtonText: string;
    blockquote?: BlockquoteWithIconProps;
    onUserSearch?: (searchText: string) => void;
    userSearchText?: string;
    userRoleFilterOptions: UserRoleFilterOptionsProps[];
    onUserRoleFilterChange: (filters: UserRoleFilterOptionsProps[]) => void;
    onCollapseExpand?: (value: boolean) => void;
    selectedUserRoleFilters: UserRoleFilterOptionsProps[];
    isDropdownExpanded?: boolean;
    onAddOpen?: () => void;
    onEdit?: (rowToBeEdited: {
      id: string;
      name: string;
      email: string;
      role: string;
      loginID: string;
    }) => void;
    onDelete?: (id: string) => void;
    onDownloadTemplate?: () => void;
    domains?: string[];
  };

export type HostedUsersProps = IDPConnectProps &
  TableProps &
  TableActionProps &
  UploadCSVButtonProps &
  HeaderProp &
  UserRowDataItemsTooltipProps & {
    resetText: string;
    deleteUserText: string;
    editUserText: string;
    addUserText: string;
    editButtonText: string;
    deleteButtonText: string;
    uploadCSVText: string;
    onUserSearch?: (searchText: string) => void;
    blockquote?: BlockquoteWithIconProps;
    showUserAddedAlert?: boolean;
    user?: UserWithIconProps;
    alertMessage?: ReactElement | string;
    alertType?: string;
    footer: OnboardingFooterProps;
    disconnectModalOpen?: boolean;
    userSearchText?: string;
    userRoleFilterOptions: UserRoleFilterOptionsProps[];
    onCollapseExpand?: (value: boolean) => void;
    onUserRoleFilterChange: (filters: UserRoleFilterOptionsProps[]) => void;
    selectedUserRoleFilters: UserRoleFilterOptionsProps[];
    isDropdownExpanded?: boolean;
    onAddOpen?: () => void;
    onSave?: (rowToBeAdded: {
      name: string;
      email: string;
      role: string;
      loginID: string;
    }) => void;
    onEdit?: (rowToBeEdited: {
      id: string;
      name: string;
      email: string;
      role: string;
      loginID: string;
    }) => void;
    onDelete: (id: string) => void;
    onDownloadTemplate: () => void;
    onDisconnect?: () => void;
    domains?: string[];
    loading: boolean;
  };

type HeaderProp = {
  heading: string;
  id?: string;
};

function Header({ heading, id }: HeaderProp) {
  const { t } = useTranslation();

  return (
    <h1
      className="typography-header4 text-semantic-content-base-primary mb-rem-120"
      data-testid={getDataTestId("heading", id)}
    >
      {t(heading)}
    </h1>
  );
}
function IDPConnect({
  id,
  connected,
  idpName,
  isModalActive,
  setModalActive,
  connectedButton,
  disconnectedButton,
}: IDPConnectProps) {
  const handleDisconnectModal = () => {
    setModalActive?.(!isModalActive);
  };
  const { t } = useTranslation();

  return (
    <div className="flex items-center text-semantic-surface-base-brand">
      {connected ? (
        <Button
          data-testid={getDataTestId("disconnect-idp-btn", id)}
          variant="tertiary"
          prefixIcon={<FontAwesomeIcon icon={faCircleMinus} />}
          onClick={handleDisconnectModal}
        >
          {`${t(disconnectedButton)} ${idpName}`}
        </Button>
      ) : (
        <ZButtonLink
          type="tertiary"
          href={ONBOARDING_PATHS.id_provider}
          data-testid={getDataTestId("connect-idp-btn", id)}
        >
          <FontAwesomeIcon
            icon={faFingerprint}
            aria-label={t("CONNECTED_ICON")}
          />
          <span className="ml-rem-80">{t(connectedButton)}</span>
        </ZButtonLink>
      )}
    </div>
  );
}

function HostedUsersContent({
  id,
  blockquote,
  connected,
  isModalActive,
  isUploadCSV = false,
  onUserSearch,
  setModalActive,
  setShowUserModal,
  tableColumnFn,
  tablePaginationData,
  uploadModalOpen,
  uploadState,
  tooltipMessage,
  rowToBeDeleted,
  rowToBeEdited,
  editModalOpen,
  userSearchText,
  userRoleFilterOptions,
  onUserRoleFilterChange,
  onCollapseExpand,
  isDropdownExpanded,
  onAddOpen,
  onEdit,
  onDelete,
  onUpload,
  onDownloadTemplate,
  setShowUploadModal,
  connectedButton,
  disconnectedButton,
  domains,
  editButtonText,
  deleteButtonText,
  uploadCSVText,
  addUserText,
  editUserText,
  deleteUserText,
  resetText,
  idpName,
  fetchRows,
  tableRef,
  failedRecords,
}: HostedUsersContentProps) {
  const [removeUser, setRemoveUserModal] = useState<TableRowItemProps | null>(
    rowToBeDeleted ?? null,
  );
  const [showEditUserModal, setEditShowUserModal] = useState(
    editModalOpen ?? false,
  );
  const [editUser, setEditUserModal] = useState<TableRowItemProps | null>(
    rowToBeEdited ?? null,
  );
  const [userRoleFilters, setUserRoleFilters] = useState<
    UserRoleFilterOptionsProps[]
  >([]);

  const handleFilteredResult = debounce((value: string) => {
    if (onUserSearch) {
      onUserSearch(value);
    }
  }, 300);
  const { t } = useTranslation();

  const columnData = tableColumnFn();
  const actionIndex: number = columnData?.findIndex(
    (item) => item.id === "action",
  );
  if (actionIndex !== -1 && columnData[actionIndex]) {
    columnData[actionIndex].renderItem = (row: TableRenderItem) => (
      <>
        {row?.item?.isActionDisabled ? (
          <Tooltip
            position="top"
            classList={["sidebar"]}
            darkMode={false}
            comp={
              <div className="typography-paragraph1 leading-none w-[145px] text-left">
                {t(tooltipMessage)}
              </div>
            }
          >
            <div className="flex gap-default">
              <ZMenu
                items={[
                  {
                    id: "edit-action",
                    name: editButtonText,
                    iconClass:
                      "fa-regular fa-pen text-semantic-content-interactive-primary-disabled cursor-default",
                    ariaLabel: t("EDIT_TABLE", { name: row.item.name }),
                  },
                  ...[
                    row?.item?.canDelete
                      ? {
                          id: "delete-action",
                          name: t(deleteButtonText),
                          iconClass:
                            "fa-regular fa-trash-can text-semantic-content-interactive-primary-disabled cursor-default",
                          ariaLabel: `DELETE-${row.item.name}`,
                        }
                      : {},
                  ],
                ]}
              />
            </div>
          </Tooltip>
        ) : (
          <div className="flex gap-default">
            <ZMenu
              items={[
                {
                  id: "edit-action",
                  name: editButtonText,
                  iconClass: "fa-regular fa-pen text-semantic-brand-default",
                  ariaLabel: t("EDIT_TABLE", { name: row.item.name }),
                },
                ...[
                  row?.item?.canDelete
                    ? {
                        id: "delete-action",
                        name: deleteButtonText,
                        iconClass:
                          "fa-regular fa-trash-can text-semantic-brand-default",
                        ariaLabel: `DELETE-${row.item.name}`,
                      }
                    : {},
                ],
              ]}
              onItemSelect={(item: MenuItem) => {
                if (item.id === "delete-action") {
                  setRemoveUserModal(row.item);
                }
                if (item.id === "edit-action") {
                  setEditUserModal(row.item);
                  onAddOpen?.();
                  setEditShowUserModal(true);
                }
              }}
            />
          </div>
        )}
      </>
    );
  }
  const handleUserRoleFilter = (options: UserRoleFilterOptionsProps[]) => {
    onUserRoleFilterChange(options);
    setUserRoleFilters(options);
  };

  return (
    <div>
      <UserModal
        name={editUser?.name}
        email={editUser?.email}
        role={editUser?.role?.toLocaleLowerCase()?.split(" ").join("-")}
        id={editUser?.id}
        loginID={editUser?.loginID}
        title={editUserText}
        blockquote={blockquote}
        show={showEditUserModal}
        setShowUserModal={setEditShowUserModal}
        onEdit={(user) => onEdit?.(user)}
        onClose={() => setEditUserModal(null)}
        domains={domains}
      />
      <RemoveModal
        title={deleteUserText}
        show={!!removeUser}
        loginID={removeUser?.loginID}
        onClose={() => setRemoveUserModal(null)}
        onDelete={(id: string) => onDelete?.(id)}
        id={removeUser?.id}
      />
      <UploadModal
        show={uploadModalOpen}
        title={uploadCSVText}
        state={uploadState}
        onFileDownload={() => onDownloadTemplate?.()}
        setShowUploadModal={setShowUploadModal}
        onFileUpload={(formData: FormData) => onUpload?.(formData)}
        failedRecords={failedRecords}
      />
      <div className="flex justify-between w-full items-center mb-rem-120">
        <div className="flex gap-m items-center">
          <Zselect
            id={id}
            showSelectedOptions={false}
            idAttr={"id"}
            valueAttr={"name"}
            multiSelect={true}
            enableSelectAll={false}
            flip={false}
            enableCancel={false}
            options={userRoleFilterOptions}
            overrideCollapsedView={(
              ...props: [boolean, UserRoleFilterOptionsProps[]]
            ) => {
              const show = props?.[0];
              const selectedOption = props?.[1];

              const summaryText = selectedOption
                .map((elem) => elem.name)
                .join(", ");

              return (
                <>
                  <span className={`${!show && "summary-text"}`}>
                    {!!summaryText.length ? `${t("ROLE")} = ` : t("ROLE")}
                  </span>
                  {!!summaryText.length && (
                    <span className={`${!show && "text-selected"}`}>
                      {summaryText}
                    </span>
                  )}
                </>
              );
            }}
            preSelectedOptions={userRoleFilters}
            onExpandCollapse={onCollapseExpand}
            enableClearSelection={false}
            showClearSelection={false}
            isPill
            searchOptions={{ enable: false }}
            onSelectionChange={handleUserRoleFilter}
            customClass="fix-padding-initial-render typography-paragraph1 !font-normal text-semantic-content-base-primary pill"
            showDropdownIconVariation={false}
            onActiveShowValue={false}
          />
          {!isDropdownExpanded && userRoleFilters.length > 0 && (
            <Button
              data-testid={getDataTestId("filter-reset-btn", id)}
              variant="tertiary"
              onClick={() => handleUserRoleFilter([])}
              onKeyDown={() => handleUserRoleFilter([])}
              tabIndex={0}
              prefixIcon={<FontAwesomeIcon icon={faRotateLeft} />}
            >
              {t(resetText)}
            </Button>
          )}
        </div>
        <div className="flex flex-row items-center gap-rem-120">
          <SearchBar
            id={id}
            onChange={handleFilteredResult}
            isOnlyExpanded={true}
            userSearchText={userSearchText}
          />
          <IDPConnect
            id={id}
            setModalActive={setModalActive}
            isModalActive={isModalActive}
            connected={connected}
            idpName={idpName}
            connectedButton={connectedButton}
            disconnectedButton={disconnectedButton}
          />
          {isUploadCSV && (
            <Button
              prefixIcon={<FontAwesomeIcon icon={faUpload} />}
              variant="secondary"
              onClick={() => setShowUploadModal?.(true)}
              data-testid={getDataTestId("upload-modal-btn", id)}
            >
              {t(uploadCSVText)}
            </Button>
          )}
          <Button
            variant="primary"
            prefixIcon={<FontAwesomeIcon icon={faPlus} />}
            onClick={() => {
              onAddOpen?.();
              setShowUserModal(true);
            }}
            data-testid={getDataTestId("add-user-btn", id)}
          >
            {t(addUserText)}
          </Button>
        </div>
      </div>
      <div className="flex max-h-[460px] overflow-hidden">
        <ZDataTable
          id={id}
          columns={columnData}
          pagination={tablePaginationData}
          noSelection
          fetchRows={fetchRows}
          ref={tableRef}
          loadingComponent={<TableLoader rows={7} />}
        />
      </div>
    </div>
  );
}

export default function HostedUsers({
  heading,
  blockquote,
  confirmationText,
  connected,
  isUploadCSV,
  modalText,
  onUserSearch,
  tableColumnFn,
  tablePaginationData,
  tableRowData,
  title,
  userModalOpen,
  disconnectModalOpen,
  uploadState,
  warningText,
  footer,
  tooltipMessage,
  uploadModalOpen,
  rowToBeDeleted,
  rowToBeEdited,
  rowToBeAdded,
  editModalOpen,
  userSearchText,
  userRoleFilterOptions,
  selectedUserRoleFilters,
  onUserRoleFilterChange,
  onCollapseExpand,
  isDropdownExpanded,
  onAddOpen,
  onSave,
  onEdit,
  onDelete,
  onUpload,
  onDownloadTemplate,
  setShowUploadModal,
  onDisconnect,
  connectedButton,
  disconnectedButton,
  domains,
  editButtonText,
  deleteButtonText,
  uploadCSVText,
  addUserText,
  editUserText,
  deleteUserText,
  idpName,
  resetText,
  fetchRows,
  tableRef,
  failedRecords,
  loading,
}: HostedUsersProps & UserModalProps) {
  const ID = "hosted-users";

  const [showUserModal, setShowUserModal] = useState(userModalOpen);
  const [isModalActive, setModalActive] = useState<boolean>(
    disconnectModalOpen ?? false,
  );

  return (
    <OnboardingWrapper>
      <OnboardingContent maxWidth="wide" id={ID}>
        <div className="flex flex-col flex-wrap bg-semantic-surface-base-primary">
          <div className="max-h-[600px]">
            <Header heading={heading} id={ID} />
            <WithStates
              loading={loading}
              loadingComponent={<HostedUserLoader />}
            >
              <HostedUsersContent
                id={ID}
                resetText={resetText}
                deleteUserText={deleteUserText}
                editUserText={editUserText}
                addUserText={addUserText}
                uploadCSVText={uploadCSVText}
                editButtonText={editButtonText}
                deleteButtonText={deleteButtonText}
                connected={connected}
                isUploadCSV={isUploadCSV}
                uploadState={uploadState}
                onUserSearch={onUserSearch}
                tableColumnFn={tableColumnFn}
                tablePaginationData={tablePaginationData}
                tableRowData={tableRowData}
                setShowUserModal={setShowUserModal}
                setShowUploadModal={setShowUploadModal}
                isModalActive={isModalActive}
                setModalActive={setModalActive}
                blockquote={blockquote}
                tooltipMessage={tooltipMessage}
                uploadModalOpen={uploadModalOpen}
                rowToBeDeleted={rowToBeDeleted}
                rowToBeEdited={rowToBeEdited}
                editModalOpen={editModalOpen}
                userSearchText={userSearchText}
                userRoleFilterOptions={userRoleFilterOptions}
                onUserRoleFilterChange={onUserRoleFilterChange}
                selectedUserRoleFilters={selectedUserRoleFilters}
                onCollapseExpand={onCollapseExpand}
                isDropdownExpanded={isDropdownExpanded}
                onAddOpen={onAddOpen}
                onEdit={onEdit}
                onDelete={onDelete}
                onUpload={(formData: FormData) => onUpload?.(formData)}
                onDownloadTemplate={onDownloadTemplate}
                connectedButton={connectedButton}
                disconnectedButton={disconnectedButton}
                domains={domains}
                idpName={idpName}
                fetchRows={fetchRows}
                tableRef={tableRef}
                failedRecords={failedRecords}
              />
            </WithStates>
          </div>
          <UserModal
            name={rowToBeAdded?.name}
            email={rowToBeAdded?.email}
            role={rowToBeAdded?.role?.toLocaleLowerCase()?.split(" ").join("-")}
            loginID={rowToBeAdded?.loginID}
            title={addUserText}
            blockquote={blockquote}
            show={!!showUserModal}
            setShowUserModal={setShowUserModal}
            onSave={(user) => onSave?.(user)}
            onClose={() => setShowUserModal(false)}
            domains={domains}
          />
          <DisconnectModal
            show={isModalActive}
            setModalActive={setModalActive}
            title={title}
            warningText={warningText}
            confirmationText={confirmationText}
            modalText={modalText}
            idpName={idpName}
            onDisconnect={onDisconnect}
          />
        </div>
      </OnboardingContent>
      <OnboardingFooter
        maxWidth="wide"
        {...{
          ...footer,
          leftFlowControl: footer.leftFlowControl?.map((item) => ({
            ...item,
            loading: loading,
          })),
        }}
      />
    </OnboardingWrapper>
  );
}
