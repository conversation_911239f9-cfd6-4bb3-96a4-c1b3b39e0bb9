import { useTranslation } from "react-i18next";
import { Zselect } from "@xc/legacy-components";
import { getDataTestId } from "@/utils/utils";

export type SelectOption = {
  id: string;
  label: string;
};

export type SelectProps = {
  options: SelectOption[];
  onChange: (value: SelectOption) => void;
  label: string;
  value: string;
  id?: string;
  disabled?: boolean;
};

export default function Select({
  options,
  onChange,
  label,
  value,
  id,
  disabled = false,
}: SelectProps) {
  const { t } = useTranslation();

  return (
    <div className="flex flex-col max-w-[340px] flex-initial">
      <div className="typography-paragraph2-strong text-semantic-content-base-secondary pb-rem-40">
        {t(label)}
      </div>
      <div>
        <Zselect
          disabled={disabled}
          showSelectedOptions={false}
          placeholder={t(label)}
          idAttr={"id"}
          valueAttr={"label"}
          multiSelect={false}
          enableSelectAll={false}
          flip={false}
          enableCancel={false}
          options={options}
          enableClearSelection={false}
          showClearSelection={false}
          isPill={false}
          searchOptions={{ enable: false }}
          onSelectionChange={(item: SelectOption[]) => onChange(item[0])}
          preSelectedOptions={options.filter((item) => item.id === value)}
          id={getDataTestId(id)}
        />
      </div>
    </div>
  );
}
