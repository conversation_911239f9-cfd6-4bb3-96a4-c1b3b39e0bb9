import { mutate } from "swr";
import useSWRMutation from "swr/mutation";
import { ZButton } from "@xc/legacy-components";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import { putReq } from "@/utils/apiHelper";
import { OrgAdminStatusEndpoints } from "@/configs/urls/zia/zia";
import type {
  ActivationData,
  ShowActivationAlertHandler,
} from "@/components/Activation/types";
import { getDataTestId } from "@/utils/utils";

type PendingActivationTabProps = {
  data: ActivationData;
  onActivate: () => void;
  onAlert: ShowActivationAlertHandler;
  id?: string;
};

export function PendingActivationTab({
  data,
  onActivate,
  onAlert,
  id,
}: PendingActivationTabProps) {
  const ID = getDataTestId("pending-activation", id);
  const { t } = useTranslation();
  const {
    editingUsers,
    queuedUsers,
    isEditing,
    currentUser,
    canForceActivate,
  } = data;

  const handleError = () => {
    onAlert("error", t("ACTIVATION_FAILED"));
  };

  const handleSuccess = () => {
    onActivate();
    void mutate(OrgAdminStatusEndpoints.GetStatus);
  };

  const { trigger: activate, isMutating: isActivating } = useSWRMutation(
    OrgAdminStatusEndpoints.Activate,
    putReq,
    {
      onSuccess: () => {
        // if there are multiple editing users or if the editing user is not the current user
        if (
          editingUsers.length > 1 ||
          (editingUsers.length === 1 && !editingUsers.includes(currentUser))
        ) {
          onAlert("success", t("ACTIVATION_PENDING"));
        } else {
          onAlert("success", t("ACTIVATION_COMPLETED"));
        }
        handleSuccess();
      },
      onError: handleError,
    },
  );

  const { trigger: forceActivate, isMutating: isForceActivating } =
    useSWRMutation(OrgAdminStatusEndpoints.ForceActivate, putReq, {
      onSuccess: () => {
        onAlert("success", t("ACTIVATION_COMPLETED"));
        handleSuccess();
      },
      onError: handleError,
    });

  const canActivate =
    (canForceActivate && (editingUsers.length > 0 || queuedUsers.length > 0)) ||
    isEditing;
  const disabledButtons = !canActivate || isActivating || isForceActivating;

  return (
    <>
      <div className="px-xxxl py-l flex flex-col gap-l" data-testid={ID}>
        <div className="flex flex-col gap-m text-semantic-content-base-primary">
          <span className="typography-paragraph2-strong text-semantic-content-base-primary">
            {t("ACTIVATION_STATUS_LABEL")}
          </span>
          <span
            className={classNames(
              "ml-s typography-paragraph2",
              isEditing ? "text-orange-500" : "text-grey-450",
            )}
          >
            {isEditing ? "Editing" : t("NO_PENDING")}
          </span>
        </div>
        <div className="flex flex-col gap-m">
          <span className="typography-paragraph2-strong text-semantic-content-base-primary">
            {t("CURRENTLY_EDITING")}
          </span>
          <span className="ml-s typography-paragraph2 text-grey-450 inline-flex flex-col gap-s">
            {editingUsers.length
              ? editingUsers.map((user, index) => (
                  <span key={user} data-testid={getDataTestId(index, ID)}>
                    {user}
                  </span>
                ))
              : t("NO_ADMIN")}
          </span>
        </div>
      </div>
      <div className="p-l flex flex-col gap-l mt-auto">
        <ZButton
          text={t("ACTIVATE")}
          disabled={disabledButtons}
          onButtonClick={activate}
          id={getDataTestId("activate", ID)}
        />
        {canForceActivate && (
          <ZButton
            id={ID}
            type="secondary"
            text={t("FORCE_ACTIVATE")}
            disabled={disabledButtons}
            onButtonClick={forceActivate}
          />
        )}
      </div>
    </>
  );
}
