import { describe, expect, it, vi } from "vitest";
import { render } from "@testing-library/react";
import { ActivationPopover } from "./ActivationPopover";
import * as hooks from "./useActivationData";
import { ActivationData } from "@/components/Activation/types";
import I18nInit from "@/app/I18nInit";

const activationButtonId = "z-button-activation-popover-pending-activation-activate";
const forceActivationButtonId = "z-button-activation-popover-pending-activation";

describe("Activation Popover", () => {
  const renderPopover = () => render(
    <I18nInit>
      <ActivationPopover />
    </I18nInit>,
  );

  it("disables activation button when there are no edits", () => {
    vi.spyOn(hooks, "useActivationData").mockImplementation((): ActivationData => {
      return {
        canForceActivate: false,
        editingUsers: [],
        queuedUsers: [],
        currentUser: "",
        showBadge: true,
        isEditing: false,
        isQueued: false,
      };
    });
    const { getByTestId, queryByTestId } = renderPopover();
    // ZButton doesn't use the disabled attribute, so we check for the class instead
    expect(getByTestId(activationButtonId)).toHaveClass("disabled");

    // non admin user does not have the force activate button
    expect(queryByTestId(forceActivationButtonId)).not.toBeInTheDocument();
  });

  it("enables activation button when user is admin and there are editing users", () => {
    vi.spyOn(hooks, "useActivationData").mockImplementation((): ActivationData => {
      return {
        canForceActivate: true,
        editingUsers: [""],
        queuedUsers: [],
        currentUser: "",
        showBadge: true,
        isEditing: false,
        isQueued: false,
      };
    });

    const { getByTestId } = renderPopover();
    expect(getByTestId(activationButtonId)).not.toHaveClass("disabled");
    expect(getByTestId(forceActivationButtonId)).not.toHaveClass("disabled");
  });

  it("enables activation button when user is admin and there are queued users", () => {
    vi.spyOn(hooks, "useActivationData").mockImplementation((): ActivationData => {
      return {
        canForceActivate: true,
        editingUsers: [""],
        queuedUsers: [],
        currentUser: "",
        showBadge: true,
        isEditing: false,
        isQueued: false,
      };
    });

    const { getByTestId } = renderPopover();
    expect(getByTestId(activationButtonId)).not.toHaveClass("disabled");
    expect(getByTestId(forceActivationButtonId)).not.toHaveClass("disabled");
  });

  it("enables activation button when the user is the editing user", () => {
    vi.spyOn(hooks, "useActivationData").mockImplementation((): ActivationData => {
      return {
        canForceActivate: false,
        editingUsers: [],
        queuedUsers: [],
        currentUser: "",
        showBadge: true,
        isEditing: true,
        isQueued: false,
      };
    });

    const { getByTestId, queryByTestId } = renderPopover();
    expect(getByTestId(activationButtonId)).not.toHaveClass("disabled");
    expect(queryByTestId(forceActivationButtonId)).not.toBeInTheDocument();
  });
});