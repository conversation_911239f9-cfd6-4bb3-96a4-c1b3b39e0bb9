export type AdminStatusMapStatus =
  | "ADM_LOGGED_IN"
  | "ADM_EDITING"
  | "ADM_ACTV_QUEUED"
  | "ADM_ACTIVATING"
  | "ADM_ACTV_DONE"
  | "ADM_ACTV_FAIL"
  | "ADM_EXPIRED";

export type OrgEditStatus =
  | "EDITS_PRESENT"
  | "EDITS_CLEARED"
  | "EDITS_ACTIVATED_ON_RESTART";

export type OrgLastActivateStatus =
  | "CAC_ACTV_UNKNOWN"
  | "CAC_ACTV_UI"
  | "CAC_ACTV_OLD_UI"
  | "CAC_ACTV_SUPERADMIN"
  | "CAC_ACTV_AUTOSYNC"
  | "CAC_ACTV_TIMER"
  | "CAC_ACTV_SESSION_LOGOUT";

export type OrgAdminStatus = {
  adminStatusMap: Record<string, AdminStatusMapStatus>;
  orgEditStatus: OrgEditStatus;
  orgLastActivateStatus: OrgLastActivateStatus;
};

export type ActivationData = {
  editingUsers: string[];
  queuedUsers: string[];
  currentUser: string;
  showBadge: boolean;
  isEditing: boolean;
  isQueued: boolean;
  canForceActivate: boolean;
};

export type ShowActivationAlertHandler = (
  type: "success" | "error",
  message: string,
) => void;
