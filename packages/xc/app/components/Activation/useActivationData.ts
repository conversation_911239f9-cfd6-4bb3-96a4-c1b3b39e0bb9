import { useContext } from "react";

import type {
  ActivationData,
  OrgAdminStatus,
} from "@/components/Activation/types";
import { UserInfoContext } from "@/context/UserInfoContext";
import { useZiaRbac } from "@/hooks/useZiaRbac";

export const useActivationData = (data?: OrgAdminStatus): ActivationData => {
  const { userInfo } = useContext(UserInfoContext);
  const { roleBasedAccess, entitled } = useZiaRbac();

  const userEmail = userInfo.loginName;

  const adminStatusMap = data?.adminStatusMap ?? {};

  const editingUsers = Object.entries(adminStatusMap)
    .filter(([, status]) => status === "ADM_EDITING")
    .map(([user]) => user);

  const queuedUsers = Object.entries(adminStatusMap)
    .filter(([, status]) => status === "ADM_ACTV_QUEUED")
    .map(([user]) => user);

  const currentUserStatus = Object.entries(adminStatusMap).find(
    ([user]) => user === userEmail,
  )?.[1];

  return {
    editingUsers,
    queuedUsers,
    currentUser: userEmail ?? "",
    showBadge: editingUsers.length > 0,
    isEditing: currentUserStatus === "ADM_EDITING",
    isQueued: currentUserStatus === "ADM_ACTV_QUEUED",
    canForceActivate: entitled ? roleBasedAccess.getRank() === 0 : false,
  };
};
