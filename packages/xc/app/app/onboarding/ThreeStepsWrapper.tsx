"use client";

import use<PERSON><PERSON> from "swr";
import { Alert } from "@xc/legacy-components";
import { type ReactElement, useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import useSWRMutation from "swr/mutation";
import {
  type ErrorType,
  showErrorAlert,
  type ConfiguredUserResponseBody,
  type ConnectedUserResponseBody,
  removeAlertMessage,
} from "./apiHelper";
import ThreeSteps, {
  type ThreeStepProps,
} from "@/components/pages/ThreeSteps/ThreeSteps";
import {
  EditIconButton,
  THREE_STEPS_DATA,
} from "@/components/pages/ThreeSteps/ThreeSteps.data";
import {
  getOnboardStatusEndpoint,
  ThreeStepsEndpoints,
} from "@/configs/urls/onboarding/onboarding";
import { getReq, postReq, API_ENDPOINTS } from "@/utils/apiHelper";
import ONBOARDING_PATHS from "@/configs/locales/OnboardingPaths";
import {
  type Statuses,
  updateFlowStatus,
  type StatusResponseData,
} from "@/utils/onboarding";
import {
  ACTIVE,
  SKIPPED,
  COMPLETE,
  POLICY_PARTIAL,
  TRAFFIC_DONE,
  USER_DONE,
  DEFAULT,
  USER_DATA,
  TRAFFIC_DATA,
} from "@/configs/constants";
import { useFlags } from "@/context/FeatureFlags";
import { getDataTestId } from "@/utils/utils";

const POLICY_HREF_MAP = [
  ONBOARDING_PATHS.policy_url,
  ONBOARDING_PATHS.policy_ssl,
  ONBOARDING_PATHS.policy_ctp,
  ONBOARDING_PATHS.policy_dpl,
  ONBOARDING_PATHS.policy_privacy,
  ONBOARDING_PATHS.policy_misc,
  ONBOARDING_PATHS.policy_review,
];

export default function ThreeStepsWrapper() {
  const [_stage, setStage] = useState("");
  const [loader, setLoader] = useState(true);
  const [configuredUsersData, setConfiguredUsersData] = useState<number>(0);
  const [connectedUsersData, setConnectedUsersData] = useState<number>(0);
  const [alertMessage, setAlertMessage] = useState<ReactElement | string>();
  const [alertType, setAlertType] = useState("");
  const [threeStepsData, setThreeStepsData] = useState<ThreeStepProps>({
    ...THREE_STEPS_DATA.default,
  });
  const [showSkipModal, setShowSkipModal] = useState<boolean>(false);
  const pathName = usePathname();
  const { t } = useTranslation();
  const router = useRouter();

  const updateFlowStatusHandler = {
    onSuccess: () => {
      router.replace("/");
    },
    onError: (err: Error) => {
      setLoader(false);
      showErrorAlert(err?.cause as ErrorType, setAlertType, setAlertMessage);
      removeAlertMessage(setAlertType, setAlertMessage);
    },
  };

  const updateOnboardFlowStatus = useSWRMutation(
    `${API_ENDPOINTS.ZUXP}/onboard-status`,
    postReq,
    updateFlowStatusHandler,
  );

  const { can } = useFlags();

  const hasSkip = can("showOnboardingSkip");

  const { isLoading: statusLoading, mutate: statusMutate } =
    useSWR<StatusResponseData>(getOnboardStatusEndpoint(), getReq, {
      onSuccess: (data: StatusResponseData) => {
        setLoader(false);
        if (data) {
          const { steps } = data;
          const step1 = steps?.[1];
          const step2 = steps?.[2];
          const step3 = steps?.[3];

          if (
            step3?.substeps &&
            Object.values(step3?.substeps)?.find(
              (item) => item?.status === COMPLETE,
            )
          ) {
            let count = 0;
            Object.values(data.steps[3].substeps).forEach((item) => {
              if (item.status === COMPLETE) {
                count = count + 1;
              }
            });
            setStage(POLICY_PARTIAL);
            //TODO: Make values shown in partial policy respective to onboarding status
            setThreeStepsData({
              ...THREE_STEPS_DATA[POLICY_PARTIAL]({
                steps: count,
                href: POLICY_HREF_MAP[count],
                userStatus: step1.status,
                trafficStatus: step2.status,
                hasSkip,
              }),
            });
          } else if (step2?.status === COMPLETE) {
            setStage(TRAFFIC_DONE);
            setThreeStepsData({
              ...THREE_STEPS_DATA[TRAFFIC_DATA]({
                status: COMPLETE,
                usersStatus: step1.status,
                hasSkip,
              }),
            });
          } else if (step2?.status === SKIPPED) {
            setThreeStepsData({
              ...THREE_STEPS_DATA[TRAFFIC_DATA]({
                status: SKIPPED,
                usersStatus: step1.status,
                hasSkip,
              }),
            });
          } else if (step1?.status === COMPLETE) {
            setStage(USER_DONE);
            setThreeStepsData({
              ...THREE_STEPS_DATA[USER_DATA]({ status: COMPLETE, hasSkip }),
            });
          } else if (step1?.status === SKIPPED) {
            setThreeStepsData({
              ...THREE_STEPS_DATA[USER_DATA]({ status: SKIPPED, hasSkip }),
            });
          } else {
            setStage(DEFAULT);
          }
        }
      },
      onError: (err: Error) => {
        setLoader(false);
        showErrorAlert(err?.cause as ErrorType, setAlertType, setAlertMessage);
      },
    });

  useEffect(() => {
    void statusMutate();
  }, [pathName, statusMutate]);
  const callAPIsBasedOnStage = (): {
    callUserAPI: boolean;
    callConnectedApi: boolean;
    callCheckIdpApi: boolean;
  } => {
    let callUserAPI = false;
    let callConnectedApi = false;
    let callCheckIdpApi = false;
    if (_stage === POLICY_PARTIAL || _stage === TRAFFIC_DONE) {
      callConnectedApi = threeStepsData.steps[1].theme === COMPLETE && true;
      callUserAPI = threeStepsData.steps[0].theme === COMPLETE && true;
    } else if (_stage === USER_DONE) {
      callUserAPI = true;
    } else if (_stage === DEFAULT) {
      callCheckIdpApi = true;
    }

    return { callUserAPI, callConnectedApi, callCheckIdpApi };
  };
  const { callUserAPI, callConnectedApi, callCheckIdpApi } =
    callAPIsBasedOnStage();

  const { isLoading: isIdpCheckLoading } = useSWR<{ totalRecord: number }>(
    callCheckIdpApi ? ThreeStepsEndpoints.connectIDPCheck : null,
    getReq,
    {
      onSuccess: (data: { totalRecord: number }) => {
        if (data?.totalRecord > 0) {
          void updateFlowStatus(1, 1, COMPLETE, () =>
            router.push(ONBOARDING_PATHS.threeSteps),
          );
          void statusMutate();

          return setThreeStepsData({
            ...THREE_STEPS_DATA[USER_DATA]({ status: COMPLETE, hasSkip }),
          });
        }
        setThreeStepsData({ ...THREE_STEPS_DATA.default });
      },
      onError: (err: Error) => {
        setLoader(false);
        showErrorAlert(err?.cause as ErrorType, setAlertType, setAlertMessage);
      },
    },
  );

  const { isLoading: isConfiguredUsersLoading, data: configUserData } =
    useSWR<ConfiguredUserResponseBody>(
      callUserAPI ? ThreeStepsEndpoints.configuredUsers({}) : null,
      getReq,
      {
        onError: (err: Error) => {
          setLoader(false);
          showErrorAlert(
            err?.cause as ErrorType,
            setAlertType,
            setAlertMessage,
          );
        },
      },
    );
  const { isLoading: isConnectedUserLoading, data: connectedUserData } =
    useSWR<ConnectedUserResponseBody>(
      callConnectedApi ? ThreeStepsEndpoints.connectedUsers({}) : null,
      getReq,
      {
        onError: (err: Error) => {
          setLoader(false);
          showErrorAlert(
            err?.cause as ErrorType,
            setAlertType,
            setAlertMessage,
          );
        },
      },
    );

  const handleSkip = () => {
    if (threeStepsData.steps[0].theme === ACTIVE) {
      void updateFlowStatus(1, 1, SKIPPED, () =>
        router.push(ONBOARDING_PATHS.threeSteps),
      );
      setThreeStepsData({
        ...THREE_STEPS_DATA[USER_DATA]({ status: SKIPPED, hasSkip }),
      });
    }
    if (threeStepsData.steps[1].theme === ACTIVE) {
      void updateFlowStatus(2, 1, SKIPPED, () =>
        router.push(ONBOARDING_PATHS.threeSteps),
      );
      setThreeStepsData({
        ...THREE_STEPS_DATA[TRAFFIC_DATA]({
          status: SKIPPED,
          usersStatus: threeStepsData.steps[0].theme as Statuses,
          hasSkip,
        }),
      });
    }
    if (threeStepsData.steps[2].theme === ACTIVE) {
      setShowSkipModal(true);
    }
  };

  useEffect(() => {
    if (configUserData?.totalRecord) {
      setConfiguredUsersData(configUserData.totalRecord);
    }
  }, [configUserData]);

  useEffect(() => {
    if (connectedUserData?.totalCount) {
      setConnectedUsersData(connectedUserData.totalCount);
    }
  }, [connectedUserData]);

  useEffect(() => {
    if (
      callUserAPI &&
      callConnectedApi &&
      configUserData &&
      connectedUserData
    ) {
      setThreeStepsData((prevData: ThreeStepProps) => {
        const newData = { ...prevData };
        const { steps } = newData;
        steps[0].children = (props) =>
          getStepData(
            t("OB_TS_CONFIGURED_USERS"),
            configuredUsersData,
            ONBOARDING_PATHS.hosted_users,
            props?.id,
          );

        steps[1].children = (props) =>
          getStepData(
            t("OB_TS_CONNECTED_USERS"),
            connectedUsersData,
            ONBOARDING_PATHS.secure_traffic,
            props?.id,
          );

        return newData;
      });
    } else if (callUserAPI && configUserData && !callConnectedApi) {
      setThreeStepsData((prevData: ThreeStepProps) => {
        const newData = { ...prevData };
        const { steps } = newData;
        steps[0].children = (props) =>
          getStepData(
            t("OB_TS_CONFIGURED_USERS"),
            configuredUsersData,
            ONBOARDING_PATHS.hosted_users,
            props?.id,
          );

        return newData;
      });
    }
  }, [
    _stage,
    configuredUsersData,
    connectedUsersData,
    callConnectedApi,
    callUserAPI,
    configUserData,
    connectedUserData,
    t,
  ]);

  const getStepData = (
    title: string,
    count: number,
    href: string,
    id?: string,
  ) => (
    <>
      <p
        className="typography-header5 text-semantic-content-base-primary"
        data-testid={getDataTestId("sub-heading", id)}
      >
        {title}
      </p>
      <p
        className="typography-header3 text-semantic-content-base-primary py-rem-40"
        data-testid={getDataTestId("count", id)}
      >
        {count}
      </p>
      <EditIconButton href={href} header={title} id={id} />
    </>
  );

  const isLoading =
    isIdpCheckLoading ||
    isConfiguredUsersLoading ||
    isConnectedUserLoading ||
    statusLoading ||
    loader;

  return (
    <>
      {alertMessage && (
        <Alert
          alert={{
            message: alertMessage,
            type: alertType,
          }}
        />
      )}
      {threeStepsData && (
        <ThreeSteps
          {...threeStepsData}
          showSkipModal={showSkipModal}
          setShowSkipModal={setShowSkipModal}
          onSkipStep={handleSkip}
          loading={isLoading}
          onSkipOnboarding={() =>
            void updateOnboardFlowStatus.trigger({
              step: 4,
              status: COMPLETE,
            })
          }
        />
      )}
    </>
  );
}
