"use client";

import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
import { Spinner } from "@/components/Spinner/Spinner";
import "./RiskAssetWrapper.scss";
import { getSystemTheme } from "@/utils/themeUtils";

const RiskAssetWrapper = dynamic(() => import("./RiskAssetWrapper"), {
  ssr: false,
  loading: () => <Spinner size="2xl" defaultClass="min-h-[100vh]" />,
});

export default function Page() {
  const router = useRouter();
  const theme = getSystemTheme();
  type ClickHandlers = {
    navigate: (path: string, additionalData: { assetId: string }) => void;
  };

  const clickHandlers: ClickHandlers = {
    navigate: (path, additionalData) => {
      if (path === "assetdetails") {
        const { assetId } = additionalData;
        router.push(`/analytics/risk360/assets/${assetId}`);
      }
    },
  };

  return <RiskAssetWrapper clickHandlers={clickHandlers} theme={theme} />;
}
