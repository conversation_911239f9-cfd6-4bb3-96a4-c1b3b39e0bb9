"use client";

import { usePathname, useSearchParams } from "next/navigation";
import { useEffect, useRef, useLayoutEffect } from "react";
import { loadScript, loadStyleSheet, linkExists, scriptExists } from "@up/std";

import {
  usePreferences,
  type UserPreference,
} from "@/context/UserPreferenceContext";
import { getCloudList, getSelectedZiaCloud } from "@/utils/multiCloudConfig";

declare const globalThis: {
  initZTW: ({ userPreference }: { userPreference: UserPreference }) => void;
  ztwInitialized: boolean;
  ztwInternalRouting: boolean;
  ztwNavigate?: (to: string, options?: { replace?: boolean }) => void;
};
const APP_PREFIX = "/ec";
declare global {
  // eslint-disable-next-line @typescript-eslint/consistent-type-definitions
  interface Window {
    ZtwUxpApp: {
      cloudName?: string;
      tenantId?: string;
    };
  }
}

// Initialize the ZtwUxpApp object with default values
if (typeof window !== "undefined") {
  window.ZtwUxpApp = window.ZtwUxpApp || { cloudName: "", tenantId: "" };
}

const Page = () => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { userPreference } = usePreferences();
  const prevPathnameRef = useRef<string | null>(null);
  const stylesLoadedRef = useRef(false);
  const cloudName = getSelectedZiaCloud();

  useLayoutEffect(() => {
    window.ZtwUxpApp = {
      ...window.ZtwUxpApp,
    };
  }, []);

  //pass cloudname and tenant id to ztw
  useEffect(() => {
    // const cloudName = getSelectedZiaCloud();
    const cloudList = getCloudList();
    const selectedCloud = cloudList?.ztw?.find(
      (item) => item?.cloudName === cloudName,
    );
    window.ZtwUxpApp.cloudName = selectedCloud?.cloudName ?? "";
    window.ZtwUxpApp.tenantId = selectedCloud?.tenantId ?? "";
  }, [cloudName]);

  // Load stylesheet before rendering HTML content
  useEffect(() => {
    if (window.location.pathname.startsWith(APP_PREFIX)) {
      const stylesheet1Path = `${APP_PREFIX}/css/ecmain.css`;
      if (!stylesLoadedRef.current && !linkExists(stylesheet1Path)) {
        // Load stylesheet if not already loaded
        loadStyleSheet(stylesheet1Path);
        stylesLoadedRef.current = true; // Update stylesLoadedRef
      }
    }

    // Clean up stylesheet when pathname changes
    return () => {
      const head = document.head;
      const linkTags = head.querySelectorAll(`link[href^="${APP_PREFIX}"]`);
      stylesLoadedRef.current = false;
      if (!window.location.pathname.startsWith(`${APP_PREFIX}`)) {
        linkTags.forEach((link) => head.removeChild(link));
      }
    };
  }, [pathname]);

  useEffect(() => {
    if (typeof globalThis.ztwNavigate === "function") {
      let ztwPath = pathname.slice(pathname.indexOf("/", 1));
      if (window.location.search !== "") {
        ztwPath = ztwPath.concat(window.location.search);
      }
      globalThis.ztwNavigate(ztwPath, {
        replace: true,
      });
    }
  }, [pathname, userPreference]);

  // Effect to load scripts on pathname change or anchor tag click and clean up on unmount
  useEffect(() => {
    if (window.location.pathname.startsWith(APP_PREFIX)) {
      const script1Path = `${APP_PREFIX}/js/main.js`;

      // Load new scripts
      if (!scriptExists(script1Path)) {
        const script = loadScript(script1Path);
        script.onload = () => {
          if (typeof globalThis.initZTW === "function") {
            globalThis.initZTW({ userPreference });
            globalThis.ztwInitialized = true;
          }
        };
      } else if (
        typeof globalThis.initZTW === "function" &&
        !globalThis.ztwInitialized
      ) {
        globalThis.ztwInitialized = true;
        globalThis.initZTW({ userPreference });
      }
      // Update references
      prevPathnameRef.current = pathname;

      // Clean up scripts on pathname change
      return () => {
        if (!window.location.pathname.startsWith(APP_PREFIX)) {
          window.dispatchEvent(new CustomEvent("ztwpathchange"));
          if (typeof globalThis.ztwNavigate === "function") {
            delete globalThis.ztwNavigate;
          }
          globalThis.ztwInitialized = false;
        }
      };
    }
  }, [pathname, searchParams, userPreference]);

  return <></>;
};

export default Page;
