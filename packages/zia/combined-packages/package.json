{"name": "@zia/combined-packages", "version": "1.0.0", "description": "Combined ZIA Packages", "type": "module", "exports": {"./manifest": "./manifest.json", "./styles/*": "./styles/*"}, "files": ["dist", "styles"], "scripts": {"build": "tsc && node ./build/build.js", "postinstall": "tsc && node ./build/copy.js", "copy": "tsc && node ./build/copy.js"}, "dependencies": {"@zuxp/zia-62.2503": "npm:@zuxp/zia@62.2503.7-4a49c4f4ccc5", "@zuxp/zia-62.2504": "npm:@zuxp/zia@62.2504.6-a60b7c035671", "@zuxp/zia-62.2505": "npm:@zuxp/zia@62.2505.1-713e337396f9", "@zuxp/zia-63.0": "npm:@zuxp/zia@63.0.28-ea4d7cadf5bf"}, "devDependencies": {"typescript": "5.7.3", "prettier": "3.5.2", "@types/node": "^22.13.10", "@up/prettier-config": "workspace:*", "@up/typescript-config": "workspace:*"}}