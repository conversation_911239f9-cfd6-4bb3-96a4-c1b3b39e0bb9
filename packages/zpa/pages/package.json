{"name": "@zpa/pages", "version": "0.0.1", "description": "ZPA pages", "main": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest run", "test:ui": "vitest --ui", "test:watch": "vitest --watch", "test:coverage": "vitest run --coverage --config ./vitest.config.mjs", "prettier:config": "prettier 'src/**/*.{ts,js}'", "prettier:check": "pnpm prettier:config --check", "prettier": "pnpm prettier:config --write", "lint": "eslint ./src", "lint:fix": "eslint ./src --fix", "tsc:check": "tsc -p .", "verify": "pnpm prettier:check && pnpm lint && pnpm tsc:check"}, "dependencies": {"clsx": "2.1.1", "react-aria": "3.37.0", "tailwind-merge": "2.6.0"}, "devDependencies": {"@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.2.0", "@types/node": "22.13.5", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@up/eslint-config": "workspace:*", "@up/prettier-config": "workspace:*", "@up/typescript-config": "workspace:*", "@vitejs/plugin-react": "4.3.4", "@vitest/coverage-istanbul": "3.1.1", "@vitest/ui": "3.0.3", "autoprefixer": "10.4.20", "css-loader": "7.1.2", "eslint": "8.57.1", "postcss": "8.5.3", "postcss-url": "10.1.3", "postcss-loader": "8.1.1", "prettier": "3.5.2", "react": "18.3.1", "react-dom": "18.3.1", "style-loader": "3.3.3", "tailwindcss": "3.4.17", "tsup": "8.3.5", "type-fest": "4.37.0", "typescript": "5.7.3", "vite-tsconfig-paths": "5.1.4", "vitest": "3.1.1"}, "peerDependencies": {"react": ">= 18.3.1", "react-dom": ">= 18.3.1", "@zs-nimbus/core": "catalog:nimbus", "@zs-nimbus/dataviz-colors": "catalog:nimbus", "@zs-nimbus/foundations": "catalog:nimbus"}, "prettier": "@up/prettier-config"}