import type {
  Headers,
  Options,
  RequestOptions,
  CustomRespone,
  APIError,
} from "types/fetch";
import { Methods } from "types/fetch";

class FetchService {
  static defaultHeaders: Headers = {
    Accept: "application/json",
    "Content-Type": "application/json",
  };

  private static token: string;

  static getToken() {
    return this.token;
  }

  static setToken(token: string) {
    this.token = token;
  }

  static async handlerResponse<T>(
    response: Response,
    method: Methods,
  ): Promise<T> {
    if (!response.ok) {
      const errorData = (await response.json()) as APIError;
      return this.handleError(errorData);
    }

    if (method === Methods.DELETE || method === Methods.PUT) {
      return { success: true } as T;
    }

    const res = await response.json();
    return { success: true, response: res } as T;
  }

  static handleError<T>(error: APIError) {
    return { success: false, error } as T;
  }

  static async request<T>(
    endpoint: string,
    options: RequestOptions,
  ): Promise<T> {
    const {
      method = Methods.GET,
      body = null,
      headers = {},
      ...rest
    } = options;

    const requestOptions: RequestInit = {
      cache: "default",
      credentials: "include",
      method,
      headers: {
        Authorization: `Bearer ${this.getToken()}`,
        ...this.defaultHeaders,
        ...headers,
      },
      ...rest,
    };

    if (body) {
      requestOptions.body = body;
    }

    try {
      const response = await fetch(endpoint, requestOptions);
      return await this.handlerResponse<T>(response, method);
    } catch (error) {
      return this.handleError(error as APIError);
    }
  }

  static async get<T>(
    endpoint: string,
    options?: Options,
  ): Promise<CustomRespone<T>> {
    return this.request<CustomRespone<T>>(endpoint, {
      method: Methods.GET,
      ...options,
    });
  }

  static async post<T>(
    endpoint: string,
    options?: Options,
  ): Promise<CustomRespone<T>> {
    return this.request<CustomRespone<T>>(endpoint, {
      method: Methods.POST,
      ...options,
    });
  }

  static async put<T>(
    endpoint: string,
    options?: Options,
  ): Promise<CustomRespone<T>> {
    return this.request<CustomRespone<T>>(endpoint, {
      method: Methods.PUT,
      ...options,
    });
  }

  static async delete<T>(
    endpoint: string,
    options?: Options,
  ): Promise<CustomRespone<T>> {
    return this.request<CustomRespone<T>>(endpoint, {
      method: Methods.DELETE,
      ...options,
    });
  }
}

export default FetchService;
