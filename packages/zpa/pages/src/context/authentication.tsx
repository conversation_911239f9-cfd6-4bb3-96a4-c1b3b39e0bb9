import { createContext, type ReactNode, useContext, useMemo } from "react";

export type Authentication = {
  customerId: string;
  token: string;
};

type AuthenticationProviderProps = Authentication & {
  children: ReactNode;
};

const AuthenticationContext = createContext<Authentication | null>(null);

export const AuthenticationProvider = ({
  customerId,
  token,
  children,
}: AuthenticationProviderProps) => {
  const value = useMemo(
    () => ({
      customerId,
      token,
    }),
    [customerId, token],
  );

  return (
    <AuthenticationContext.Provider value={value}>
      {children}
    </AuthenticationContext.Provider>
  );
};

export const useAuthenticationContext = () => {
  const context = useContext(AuthenticationContext);
  if (context === null) {
    throw new Error(
      "Authentication context is not provided. Please wrap the parent component with `AuthenticationProvider`.",
    );
  }

  return context;
};
