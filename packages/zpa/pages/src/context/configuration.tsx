import { createContext, type ReactNode, useContext, useMemo } from "react";

export type Configuration = {
  mgmtApi: string;
};

type ConfigurationProviderProps = Configuration & {
  children: ReactNode;
};

const ConfigurationContext = createContext<Configuration | null>(null);

export const ConfigurationProvider = ({
  mgmtApi,
  children,
}: ConfigurationProviderProps) => {
  const value = useMemo(
    () => ({
      mgmtApi,
    }),
    [mgmtApi],
  );

  return (
    <ConfigurationContext.Provider value={value}>
      {children}
    </ConfigurationContext.Provider>
  );
};

export const useConfigurationContext = () => {
  const context = useContext(ConfigurationContext);
  if (context === null) {
    throw new Error(
      "Configuration context is not provided. Please wrap the parent component with `ConfigurationProvider`.",
    );
  }

  return context;
};
