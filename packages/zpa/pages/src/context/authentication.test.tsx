import { render, screen } from "@testing-library/react";
import {
  AuthenticationProvider,
  useAuthenticationContext,
  type Authentication,
} from "./authentication";

describe("AuthenticationContext tests", () => {
  let props: Authentication;

  beforeEach(() => {
    props = {
      customerId: "test-customer-123",
      token: "test-auth-token-xyz",
    };
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const TestConsumerComponent = () => {
    const { customerId, token } = useAuthenticationContext();
    return (
      <>
        <div data-testid="customerId">{customerId}</div>
        <div data-testid="token">{token}</div>
      </>
    );
  };

  const TestConsumerWithoutProvider = () => {
    let errorMessage = "";
    try {
      useAuthenticationContext();
    } catch (error) {
      if (error instanceof Error) {
        errorMessage = error.message;
      }
    }
    return <div data-testid="errorMessage">{errorMessage}</div>;
  };

  it("SHOULD provide the authentication values to children", () => {
    render(
      <AuthenticationProvider {...props}>
        <TestConsumerComponent />
      </AuthenticationProvider>,
    );

    expect(screen.getByTestId("customerId").textContent).toBe(props.customerId);
    expect(screen.getByTestId("token").textContent).toBe(props.token);
  });

  describe("GIVEN component is wrapped inside AuthenticationProvider", () => {
    describe("WHEN useAuthenticationContext hook is accessed", () => {
      it("SHOULD return the context with customerId and token", () => {
        render(
          <AuthenticationProvider {...props}>
            <TestConsumerComponent />
          </AuthenticationProvider>,
        );

        expect(screen.getByTestId("customerId").textContent).toBe(
          props.customerId,
        );
        expect(screen.getByTestId("token").textContent).toBe(props.token);
      });
    });
  });

  describe("GIVEN component is not wrapped inside AuthenticationProvider", () => {
    describe("WHEN useAuthenticationContext hook is accessed", () => {
      it("SHOULD throw an error", () => {
        vi.spyOn(console, "error").mockReturnValue();

        render(<TestConsumerWithoutProvider />);
        expect(screen.getByTestId("errorMessage").textContent).toBe(
          "Authentication context is not provided. Please wrap the parent component with `AuthenticationProvider`.",
        );
      });
    });
  });
});
