import { render, screen } from "@testing-library/react";
import {
  ConfigurationProvider,
  useConfigurationContext,
  type Configuration,
} from "./configuration";

describe("ConfigurationContext tests", () => {
  let props: Configuration;
  beforeEach(() => {
    props = {
      mgmtApi: "https://api.example.com",
    };
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const TestConsumerComponent = () => {
    const { mgmtApi } = useConfigurationContext();
    return <div data-testid="mgmtApi">{mgmtApi}</div>;
  };

  const TestConsumerWithoutProvider = () => {
    let errorMessage = "";
    try {
      useConfigurationContext();
    } catch (error) {
      if (error instanceof Error) {
        errorMessage = error.message;
      }
    }
    return <div data-testid="errorMessage">{errorMessage}</div>;
  };

  it("SHOULD provide the configuration values to children", () => {
    render(
      <ConfigurationProvider {...props}>
        <TestConsumerComponent />
      </ConfigurationProvider>,
    );

    expect(screen.getByTestId("mgmtApi").textContent).toBe(props.mgmtApi);
  });

  describe("GIVEN component is wrapped inside ConfigurationProvider", () => {
    describe("WHEN useConfigurationContext hook is accessed", () => {
      it("SHOULD return the context", () => {
        render(
          <ConfigurationProvider {...props}>
            <TestConsumerComponent />
          </ConfigurationProvider>,
        );

        expect(screen.getByTestId("mgmtApi").textContent).toBe(props.mgmtApi);
      });
    });
  });

  describe("GIVEN component is not wrapped inside ConfigurationProvider", () => {
    describe("WHEN useConfigurationContext hook is accessed", () => {
      it("SHOULD throw an error", () => {
        vi.spyOn(console, "error").mockReturnValue();

        render(<TestConsumerWithoutProvider />);
        expect(screen.getByTestId("errorMessage").textContent).toBe(
          "Configuration context is not provided. Please wrap the parent component with `ConfigurationProvider`.",
        );
      });
    });
  });
});
