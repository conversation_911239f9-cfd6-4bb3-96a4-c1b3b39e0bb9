import * as am5 from "@amcharts/amcharts5";
import * as am5xy from "@amcharts/amcharts5/xy";
import {
  type GradientProps,
  type ChartScrollbarProps,
  type VerticalBarScrollBarProps,
} from "./types";

export const generateVerticalBarScrollbar = (
  chart: am5xy.XYChart,
  root: am5.Root,
  scrollData: { xAxisDataKey: string; yAxisDataKey: string; data: unknown[] },
  scrollBarProps: VerticalBarScrollBarProps & ChartScrollbarProps,
) => {
  const { xAxisDataKey, yAxisDataKey, data } = scrollData;
  const {
    axisProps: { xAxisSetting, yAxisSetting } = {},
    gradientProps,
    stacked,
    columnSetting,
    columnProps,
    roundedColumnProps,
    scrollProps,
  } = scrollBarProps;
  const {
    overlaySetting,
    scrollBackgroundSetting,
    scrollSetting,
    thumbSetting,
  } = scrollProps ?? {};
  const scrollbarX = am5xy.XYChartScrollbar.new(root, {
    orientation: "horizontal",
    height: 45,
    ...scrollSetting,
  });

  chart.set("scrollbarX", scrollbarX);

  scrollbarX.get("background")?.setAll({
    ...(scrollBackgroundSetting?.scrollColor
      ? { fill: am5.color(scrollBackgroundSetting.scrollColor) }
      : {}), // light grey color for unselected area
    ...scrollBackgroundSetting,
  });

  scrollbarX.thumb.setAll({
    fill: am5.color(thumbSetting?.thumbColor ?? "#7BA5FE"), // Color for the selected range
    fillOpacity: 0.2,
    opacity: 1,
    ...thumbSetting,
  });

  scrollbarX.overlay?.setAll({
    ...(overlaySetting?.overlayColor
      ? { fill: am5.color(overlaySetting?.overlayColor) }
      : {}), // Color for the selected range
    fillOpacity: 0, // Opacity for the selected range
    ...overlaySetting,
  });

  scrollbarX.thumb?.states?.create("hover", {
    fill: am5.color(thumbSetting?.thumbColor ?? "#7BA5FE"), // Color for the selected range
    fillOpacity: 0.8,
    opacity: 0.2,
  });

  const sbXAxis = scrollbarX.chart.xAxes.push(
    am5xy.CategoryAxis.new(root, {
      maxDeviation: 0,
      categoryField: xAxisDataKey,
      renderer: am5xy.AxisRendererX.new(root, {}),
      visible: false,
      ...xAxisSetting,
    }),
  );
  const sbYAxis = scrollbarX.chart.yAxes.push(
    am5xy.ValueAxis.new(root, {
      maxDeviation: 0,
      renderer: am5xy.AxisRendererY.new(root, {}),
      ...yAxisSetting,
    }),
  );

  const sbseries = scrollbarX.chart.series.push(
    am5xy.ColumnSeries.new(root, {
      name: "Series 2",
      xAxis: sbXAxis,
      yAxis: sbYAxis,
      valueYField: yAxisDataKey,
      categoryXField: xAxisDataKey,
      width: 20,
      ...columnSetting,
    }),
  );
  const sbcolumnTemplate = sbseries.columns.template;

  sbcolumnTemplate.setAll({
    width: 4,
    ...columnProps,
    ...roundedColumnProps,
  });

  if (gradientProps?.showGradient ?? stacked) {
    sbcolumnTemplate.adapters.add("fillGradient", function (value, target) {
      const dataContext = target.dataItem?.dataContext as {
        gradientProps: GradientProps;
      };
      const gradProps = stacked ? dataContext?.gradientProps : gradientProps;

      if (gradProps) {
        return am5.LinearGradient.new(root, {
          ...gradProps,
          stops: gradProps?.stops.map((stop) => ({
            ...stop,
            color: am5.color(stop.color),
          })),
        });
      }

      return value;
    });
  } else {
    sbcolumnTemplate.adapters.add("fill", (_, target) => {
      const { color } = target?.dataItem?.dataContext as { color: string };

      return am5.color(color);
    });
  }

  customizeGrip(scrollbarX.startGrip, root);
  customizeGrip(scrollbarX.endGrip, root);
  sbseries.data.setAll(data);
  sbXAxis.data.setAll(data);
  void sbseries.appear(1000);

  chart.bottomAxesContainer.children.push(scrollbarX);
};

export function customizeGrip(grip: am5.Button, root: am5.Root) {
  grip?.get("background")?.setAll({
    fillOpacity: 0,
    strokeOpacity: 0,
  });

  grip.get("icon")?.set("forceHidden", true);
  grip.children.unshift(
    am5.Label.new(root, {
      html: `
              <svg width="59" height="77" viewBox="0 -8 59 77" fill="none" xmlns="http://www.w3.org/2000/svg">
              <line x1="29.9385" y1="9" x2="29.9385" y2="53" stroke="#0E3896" stroke-width="2"/>
              <g filter="url(#filter0_dd_30129_70306)">
              <rect x="24.8623" y="16.3333" width="10" height="28.1111" rx="1" fill="#0E3896" shape-rendering="crispEdges"/>
              <path d="M29.2061 26.7326V34.5451C29.2061 34.8185 28.9912 35.0139 28.7373 35.0139C28.4639 35.0139 28.2686 34.8185 28.2686 34.5451V26.7326C28.2686 26.4787 28.4639 26.2639 28.7373 26.2639C28.9912 26.2639 29.2061 26.4787 29.2061 26.7326ZM31.7061 26.7326V34.5451C31.7061 34.8185 31.4912 35.0139 31.2373 35.0139C30.9639 35.0139 30.7686 34.8185 30.7686 34.5451V26.7326C30.7686 26.4787 30.9639 26.2639 31.2373 26.2639C31.4912 26.2639 31.7061 26.4787 31.7061 26.7326Z" fill="white"/>
              </g>
              <defs>
              <filter id="filter0_dd_30129_70306" x="0.862305" y="0.333313" width="58" height="76.1111" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
              <feFlood flood-opacity="0" result="BackgroundImageFix"/>
              <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
              <feOffset/>
              <feGaussianBlur stdDeviation="4"/>
              <feComposite in2="hardAlpha" operator="out"/>
              <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
              <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_30129_70306"/>
              <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
              <feOffset dy="8"/>
              <feGaussianBlur stdDeviation="12"/>
              <feComposite in2="hardAlpha" operator="out"/>
              <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.14 0"/>
              <feBlend mode="normal" in2="effect1_dropShadow_30129_70306" result="effect2_dropShadow_30129_70306"/>
              <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_30129_70306" result="shape"/>
              </filter>
              </defs>
              </svg>
      `,
    }),
  );
}
