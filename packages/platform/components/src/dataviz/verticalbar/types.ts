import {
  type IRoundedRectangleSettings,
  type IScrollbarSettings,
  type IGradientSettings,
} from "@amcharts/amcharts5";
import type * as am5 from "@amcharts/amcharts5";
import type * as am5xy from "@amcharts/amcharts5/xy";

import {
  type TooltipBackgroundSetting,
  type ChartTooltipUiProp,
  type CursorProps,
  type LegendsProps,
  type ThemeType,
} from "../common/types";

export type ChartScrollbarProps = {
  scrollSetting?: am5xy.IXYChartScrollbarSettings;
  scrollBackgroundSetting?: IScrollbarSettings & {
    scrollColor: string;
  };
  thumbSetting?: IRoundedRectangleSettings & {
    thumbColor?: string;
  };
  overlaySetting?: IGradientSettings & {
    overlayColor?: string;
  };
};

type GradStop = {
  color: string;
  opacity?: number;
  offset?: number;
};

export type GradientProps = {
  stops: GradStop[];
  showGradient?: boolean;
  rotation?: number;
};

export type LabelProps = { text: string; y: number; fill: string };

export type DualYAxisProps = {
  gradientProps: GradientProps;
  labels: LabelProps[];
  rightContainerProps?: am5.IContainerSettings;
  yAxisProps?: am5xy.IAxisRendererYSettings;
};

export type VStackBarChartDataType = {
  id: string;
  tooltipHeading?: string;
  data: Record<string, number>;
};

export type VerticalTooltipProps = {
  tooltipSetting?: am5.ITooltipSettings;
  tooltipHTML?: string;
  showTooltip?: boolean;
  tooltipData?: Record<string, string>;
  tooltipBackgroundSetting?: TooltipBackgroundSetting;
  getTooltipHtml?: (d: ChartTooltipUiProp[]) => string;
};

export type VerticalAxisLabelProps = {
  labelColor?: string;
  showCustomLabel?: boolean;
  labelFormatter?: (text?: string) => string;
  centerX?: number;
};

export type AxisTooltipProps = {
  showTooltip?: boolean;
  tooltipSetting?: am5.ITooltipSettings;
  tooltipBackgroundSetting?: am5.IRectangleSettings & { fillColor?: string };
};

export type PreZoomCategoryProps = {
  preZoom: boolean;
  firstCategory: string;
  secondCategory: string;
};

export type ZoomProps = {
  preZoomAxisProps: PreZoomCategoryProps;
};

export type VerticalAxisProps = {
  xAxisSetting?: Partial<am5xy.ICategoryAxisSettings<am5xy.AxisRenderer>> &
    AxisTooltipProps;
  yAxisSetting?: Partial<am5xy.IValueAxisSettings<am5xy.AxisRenderer>> &
    AxisTooltipProps;

  yAxisProps?: am5xy.IAxisRendererYSettings;
  xAxisProps?: am5xy.IAxisRendererXSettings;

  zoomProps?: ZoomProps;

  yGridProps?: am5xy.IGridSettings;
  xGridProps?: am5xy.IGridSettings;

  xLabelProps?: am5xy.IAxisLabelSettings & VerticalAxisLabelProps;
  yLabelProps?: am5xy.IAxisLabelSettings & VerticalAxisLabelProps;

  yAxisTitleSetting?: am5.ILabelSettings & { labelColor?: string };
  xAxisTitleSetting?: am5.ILabelSettings & { labelColor?: string };
};

export type VeticalChartProps = {
  chartSettings?: am5xy.IXYChartSettings;
  leftContainer?: am5.IContainerSettings;
  rightContainer?: am5.IContainerSettings;
  chartContainer?: am5.IContainerSettings;
};

export type VerticalBarScrollBarProps = Omit<
  VerticalBarDefaultConfigProps,
  "containerClass"
> & {
  stacked?: boolean;
};

export type VerticalBarDefaultConfigProps = {
  containerClass?: string;
  chartProps?: VeticalChartProps;

  columnProps?: am5.IRectangleSettings;
  roundedColumnProps?: am5.IRoundedRectangleSettings;
  columnSetting?: am5xy.IBaseColumnSeriesSettings;

  axisProps?: VerticalAxisProps;
  legendsProps?: LegendsProps;
  tooltipProps?: VerticalTooltipProps;
  cursorProps?: CursorProps;
  bulletProps?: am5.ILabelSettings & { bulletColor?: string };

  gradientProps?: GradientProps;
  numberFormatter?: am5.INumberFormatterSettings;
  dualYAxisProps?: DualYAxisProps;
  scrollProps?: ChartScrollbarProps;
  stackedConfig?: {
    gap: number;
  };
};

export type VerticalBarProps = {
  data: unknown[];
  config?: VerticalBarDefaultConfigProps;
  axisKeys: [string, string];
  chartId?: string;
  stacked?: boolean;
  showScrollbar?: boolean;
  theme?: ThemeType;
};
