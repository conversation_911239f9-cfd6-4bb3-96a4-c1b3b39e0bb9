import { getVerticalBarTooltipHtml } from "../utils/tooltips";
import { type ThemeType } from "../common/types";
import { type VerticalBarDefaultConfigProps } from "./types";
import { datavizColors } from "@zs-nimbus/dataviz-colors";

export const VerticalBarDefaultConfig = (
  theme: ThemeType,
): VerticalBarDefaultConfigProps => {
  return {
    containerClass: "flex h-[550px]",
    chartProps: {
      chartSettings: {
        panX: false,
        panY: false,
        pinchZoomX: false,
        pinchZoomY: false,
      },
    },
    columnProps: {
      strokeOpacity: 0,
      maxWidth: 24,
    },
    roundedColumnProps: {
      cornerRadiusTL: 5,
      cornerRadiusTR: 5,
    },
    axisProps: {
      xAxisProps: { visible: false },
      yAxisProps: {
        visible: false,
      },
      xGridProps: {
        visible: false,
      },
      yGridProps: { visible: true },
      yAxisSetting: {
        min: 0,
      },
      xLabelProps: {
        rotation: 0,
      },
    },
    tooltipProps: {
      showTooltip: true,
      getTooltipHtml: getVerticalBarTooltipHtml,
    },
    gradientProps: {
      showGradient: true,
      stops: [
        {
          color: datavizColors[theme].dataviz.categorical.primary.chart01,
          opacity: 1,
        },
        {
          color: "#4B7EEC", // No Nimbus equivalent
          opacity: 1,
        },
      ],
      rotation: 90,
    },

    scrollProps: {
      scrollSetting: {
        orientation: "horizontal",
      },
      scrollBackgroundSetting: {
        scrollColor: datavizColors[theme].dataviz.neutrals.gaps,
        orientation: "horizontal",
      },
      thumbSetting: {
        thumbColor: datavizColors[theme].dataviz.sequential.blue.scale02,
        fillOpacity: 0.2,
        opacity: 1,
      },
      overlaySetting: {
        overlayColor: datavizColors[theme].dataviz.neutrals.gaps,
      },
    },
  };
};
