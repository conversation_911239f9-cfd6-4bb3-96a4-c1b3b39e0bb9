import { useLayoutEffect, useRef } from "react";
import am5themes_Animated from "@amcharts/amcharts5/themes/Animated";
import * as am5 from "@amcharts/amcharts5";
import * as am5xy from "@amcharts/amcharts5/xy";
import { getChartLegendsAtBottom } from "../utils/legendUtils";
import { getMergedConfigs } from "../utils";
import { type ChartTooltipUiProp } from "../common/types";
import { getDatavizTheme } from "../common/datavizTheme";
import { useDualAxis } from "../hooks";
import { type VerticalBarProps, type GradientProps } from "./types";
import { VerticalBarDefaultConfig as defaultConfig } from "./VerticalBarDefaultConfig";
import { generateVerticalBarScrollbar } from "./VerticalBarScrollBar";

const VerticalBar = (props: VerticalBarProps) => {
  const {
    data,
    config,
    axisKeys,
    chartId,
    stacked,
    showScrollbar,
    theme = "light",
  } = props;
  const mergedConfig = getMergedConfigs(defaultConfig(theme), config);
  const chartRef = useRef<HTMLDivElement>(null);
  const {
    columnProps,
    roundedColumnProps,
    axisProps,
    gradientProps,
    dualYAxisProps,
    cursorProps,
    legendsProps,
    tooltipProps,
    containerClass,
    bulletProps,
    chartProps,
    columnSetting,
    numberFormatter,
    scrollProps,
  } = { ...mergedConfig };
  const [xAxisDataKey, yAxisDataKey] = axisKeys;
  const { renderDualAxis } = useDualAxis({ dualYAxisProps });

  useLayoutEffect(() => {
    if (!chartRef.current) return;

    // Setup root and chart
    const root = am5.Root.new(chartRef.current);
    const myTheme = am5.Theme.new(root);
    myTheme.rule("Grid", ["base"]).setAll({ strokeOpacity: 0.1 });
    root.setThemes([
      am5themes_Animated.new(root),
      myTheme,
      getDatavizTheme(root, theme),
    ]);
    root.numberFormatter.setAll(numberFormatter || {});

    const chart = root.container.children.push(
      am5xy.XYChart.new(root, chartProps?.chartSettings || {}),
    );
    chart.zoomOutButton.set("forceHidden", true);
    chart.leftAxesContainer.setAll(chartProps?.leftContainer || {});
    chart.rightAxesContainer.setAll(chartProps?.rightContainer || {});
    chart.chartContainer.setAll(chartProps?.chartContainer || {});

    // Extract axis configs
    const {
      xAxisSetting,
      yAxisSetting,
      xAxisProps,
      yAxisProps,
      xGridProps,
      yGridProps,
      xLabelProps,
      yLabelProps,
      yAxisTitleSetting,
      xAxisTitleSetting,
    } = axisProps || {};

    // Create Y-axis
    const yRenderer = am5xy.AxisRendererY.new(root, yAxisProps || {});
    const { centerX: yLabelCenterX = 0, ...restYLabelProps } =
      yLabelProps || {};

    yRenderer.labels.template.setAll({
      ...(yLabelProps?.labelColor
        ? { fill: am5.color(yLabelProps.labelColor) }
        : {}),
      ...(yLabelCenterX >= 0 ? { centerX: am5.percent(yLabelCenterX) } : {}),
      ...restYLabelProps,
    });
    yRenderer.grid.template.setAll(yGridProps || {});

    // Custom Y-axis labels
    if (yLabelProps?.showCustomLabel) {
      yRenderer.labels.template.adapters.add("text", (text, target) => {
        if (yLabelProps.labelFormatter) return yLabelProps.labelFormatter(text);
        if (target.dataItem?.dataContext) {
          return (target.dataItem.dataContext as { yLabel: string }).yLabel;
        }
        return text;
      });
    }

    // Create Y-axis with optional tooltip
    const yAxisTooltip = yAxisSetting?.showTooltip
      ? am5.Tooltip.new(root, {
          ...(yAxisSetting.tooltipBackgroundSetting
            ? {
                background: am5.Rectangle.new(root, {
                  ...(yAxisSetting.tooltipBackgroundSetting.fillColor
                    ? {
                        fill: am5.color(
                          yAxisSetting.tooltipBackgroundSetting.fillColor,
                        ),
                      }
                    : {}),
                  ...yAxisSetting.tooltipBackgroundSetting,
                }),
              }
            : {}),
          ...yAxisSetting.tooltipSetting,
        })
      : undefined;

    const yAxis = chart.yAxes.push(
      am5xy.ValueAxis.new(root, {
        maxDeviation: 0,
        renderer: yRenderer,
        ...(yAxisTooltip ? { tooltip: yAxisTooltip } : {}),
        ...yAxisSetting,
      }),
    );

    // Add Y-axis title if specified
    if (yAxisTitleSetting) {
      yAxis.children.unshift(
        am5.Label.new(root, {
          y: am5.p50,
          centerX: am5.p50,
          x: am5.p0,
          centerY: am5.p0,
          ...(yAxisTitleSetting.labelColor
            ? { fill: am5.color(yAxisTitleSetting.labelColor) }
            : {}),
          ...yAxisTitleSetting,
        }),
      );
    }

    // Create X-axis
    const xRenderer = am5xy.AxisRendererX.new(root, xAxisProps || {});
    xRenderer.labels.template.setAll({
      ...(xLabelProps?.labelColor
        ? { fill: am5.color(xLabelProps.labelColor) }
        : {}),
      ...xLabelProps,
    });
    xRenderer.grid.template.setAll(xGridProps || {});

    // Custom X-axis labels
    if (xLabelProps?.showCustomLabel) {
      xRenderer.labels.template.adapters.add("text", (text, target) => {
        if (xLabelProps.labelFormatter) return xLabelProps.labelFormatter(text);
        if (target.dataItem?.dataContext) {
          return (target.dataItem.dataContext as { xLabel: string }).xLabel;
        }
        return text;
      });
    }

    // Create X-axis with optional tooltip
    const xAxisTooltip = xAxisSetting?.showTooltip
      ? am5.Tooltip.new(root, {
          ...(xAxisSetting.tooltipBackgroundSetting
            ? {
                background: am5.Rectangle.new(root, {
                  ...(xAxisSetting.tooltipBackgroundSetting.fillColor
                    ? {
                        fill: am5.color(
                          xAxisSetting.tooltipBackgroundSetting.fillColor,
                        ),
                      }
                    : {}),
                  ...xAxisSetting.tooltipBackgroundSetting,
                }),
              }
            : {}),
          ...xAxisSetting.tooltipSetting,
        })
      : undefined;

    const xAxis = chart.xAxes.push(
      am5xy.CategoryAxis.new(root, {
        maxDeviation: 0,
        categoryField: xAxisDataKey,
        renderer: xRenderer,
        ...(xAxisTooltip ? { tooltip: xAxisTooltip } : {}),
        ...xAxisSetting,
      }),
    );

    // Add X-axis title if specified
    if (xAxisTitleSetting) {
      xAxis.children.unshift(
        am5.Label.new(root, {
          y: am5.p50,
          centerX: am5.p50,
          x: am5.p50,
          ...(xAxisTitleSetting.labelColor
            ? { fill: am5.color(xAxisTitleSetting.labelColor) }
            : {}),
          ...xAxisTitleSetting,
        }),
      );
    }

    // Create tooltip for the chart
    const tooltip = tooltipProps?.showTooltip
      ? am5.Tooltip.new(root, {
          getFillFromSprite: false,
          getStrokeFromSprite: false,
          autoTextColor: false,
          getLabelFillFromSprite: true,
          background: am5.PointedRectangle.new(root, {
            ...(tooltipProps.tooltipBackgroundSetting?.shadowFillColor
              ? {
                  shadowColor: am5.color(
                    tooltipProps.tooltipBackgroundSetting.shadowFillColor,
                  ),
                }
              : {}),
            ...(tooltipProps.tooltipBackgroundSetting?.strokeColor
              ? {
                  stroke: am5.color(
                    tooltipProps.tooltipBackgroundSetting.strokeColor,
                  ),
                }
              : {}),
            ...(tooltipProps.tooltipBackgroundSetting?.fillColor
              ? {
                  fill: am5.color(
                    tooltipProps.tooltipBackgroundSetting.fillColor,
                  ),
                }
              : {}),
            ...tooltipProps.tooltipBackgroundSetting,
          }),
          ...tooltipProps.tooltipSetting,
        })
      : undefined;

    // Create series
    const { key: tooltipKey, ...restTooltipData } =
      tooltipProps?.tooltipData || {};
    const series = chart.series.push(
      am5xy.ColumnSeries.new(root, {
        name: "Series 1",
        xAxis,
        yAxis,
        valueYField: yAxisDataKey,
        sequencedInterpolation: true,
        maskBullets: false,
        categoryXField: xAxisDataKey,
        ...(tooltip ? { tooltip } : {}),
        ...columnSetting,
      }),
    );

    // Configure column template
    const columnTemplate = series.columns.template;
    columnTemplate.setAll({
      cursorOverStyle: "pointer",
      cornerRadiusTL: 5,
      cornerRadiusTR: 5,
      ...(tooltip && tooltipProps?.getTooltipHtml
        ? {
            tooltipHTML: tooltipProps.getTooltipHtml([
              {
                title: xAxisDataKey,
                key: tooltipKey ?? yAxisDataKey,
                yAxisDataKey,
                ...restTooltipData,
              },
            ]),
            tooltipY: am5.p0,
          }
        : {}),
      ...columnProps,
      ...roundedColumnProps,
    });

    // Configure tooltip adapter
    if (tooltipProps?.showTooltip && tooltipProps?.getTooltipHtml) {
      series.get("tooltip")?.adapters.add("labelHTML", (value, target) => {
        const context = target._settings.tooltipTarget?.dataItem
          ?.dataContext as Record<string, any> | undefined;
        if (!context) return value;

        let tooltipData = context.tooltipData as
          | ChartTooltipUiProp[]
          | undefined;
        if (tooltipData) return tooltipProps?.getTooltipHtml?.(tooltipData);

        if (context?.values) {
          tooltipData = Object.entries(
            context.values as Record<string, string>,
          ).map(([key, value]) => ({ title: key, label: key, value }));
        }

        return tooltipData
          ? tooltipProps?.getTooltipHtml?.(tooltipData)
          : value;
      });
    }

    // Configure gradient for columns
    if ((gradientProps?.showGradient ?? stacked) && columnTemplate) {
      columnTemplate.adapters.add("fillGradient", (value, target) => {
        const dataContext = target.dataItem?.dataContext as {
          gradientProps?: GradientProps;
        };
        const gradProps = stacked ? dataContext?.gradientProps : gradientProps;

        if (gradProps?.stops) {
          return am5.LinearGradient.new(root, {
            ...gradProps,
            stops: gradProps.stops.map((stop) => ({
              ...stop,
              color: am5.color(stop.color),
            })),
          });
        }
        return value;
      });
    } else if (columnTemplate) {
      columnTemplate.adapters.add("fill", (_, target) => {
        const color = (target?.dataItem?.dataContext as { color?: string })
          ?.color;
        return color ? am5.color(color) : undefined;
      });
    }

    // Add cursor if specified
    if (cursorProps) {
      const cursor = chart.set(
        "cursor",
        am5xy.XYCursor.new(root, {
          ...(cursorProps.xCursorSetting ? { xAxis } : {}),
          ...(cursorProps.yCursorSetting ? { yAxis } : {}),
          ...cursorProps,
        }),
      );

      if (cursorProps.xCursorSetting) {
        cursor.lineX.setAll({
          strokeWidth: 2,
          strokeDasharray: [5, 5],
          ...(cursorProps.xCursorSetting.strokeColor
            ? { stroke: am5.color(cursorProps.xCursorSetting.strokeColor) }
            : {}),
          ...cursorProps.xCursorSetting,
        });
      }

      if (cursorProps.yCursorSetting) {
        cursor.lineY.setAll({
          strokeWidth: 2,
          strokeDasharray: [5, 5],
          ...(cursorProps.yCursorSetting.strokeColor
            ? { stroke: am5.color(cursorProps.yCursorSetting.strokeColor) }
            : {}),
          ...cursorProps.yCursorSetting,
        });
      }
    }

    // Add scrollbar, legends, bullets and handle zoom
    if (showScrollbar) {
      generateVerticalBarScrollbar(
        chart,
        root,
        { xAxisDataKey, yAxisDataKey, data },
        { axisProps, gradientProps, stacked, roundedColumnProps, scrollProps },
      );
    }

    if (legendsProps?.showLegend) {
      getChartLegendsAtBottom({ root, chart, legendsProps });
    }

    if (bulletProps) {
      series.bullets.push(() =>
        am5.Bullet.new(root, {
          locationY: 1,
          sprite: am5.Label.new(root, {
            text: `{${yAxisDataKey}}`,
            dy: -10,
            centerY: am5.p50,
            centerX: am5.p50,
            y: am5.p100,
            populateText: true,
            ...(bulletProps.bulletColor
              ? { fill: am5.color(bulletProps.bulletColor) }
              : {}),
            ...bulletProps,
          }),
        }),
      );
    }

    // Set data, handle zoom, animations and add dual axis
    xAxis.data.setAll(data);
    series.data.setAll(data);

    if (axisProps?.zoomProps?.preZoomAxisProps?.preZoom) {
      xAxis.events.once("datavalidated", (ev) => {
        ev.target.zoomToCategories(
          axisProps?.zoomProps?.preZoomAxisProps?.firstCategory ?? "",
          axisProps?.zoomProps?.preZoomAxisProps?.secondCategory ?? "",
        );
      });
    }

    void series.appear(1000);
    void chart.appear(1000, 100);

    if (dualYAxisProps) renderDualAxis({ root, chart });

    return () => root.dispose();
  }, [
    data,
    xAxisDataKey,
    yAxisDataKey,
    columnProps,
    roundedColumnProps,
    axisProps,
    gradientProps,
    dualYAxisProps,
    renderDualAxis,
    cursorProps,
    legendsProps,
    tooltipProps,
    bulletProps,
    chartProps,
    columnSetting,
    numberFormatter,
    stacked,
    showScrollbar,
  ]);

  return (
    <div className={containerClass} data-testid={chartId} ref={chartRef} />
  );
};

export default VerticalBar;
