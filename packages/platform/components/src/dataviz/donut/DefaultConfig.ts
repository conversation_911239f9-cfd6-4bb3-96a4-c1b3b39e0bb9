import { datavizColors } from "@zs-nimbus/dataviz-colors";
import { ThemeType } from "../common/types";
import { type DonutProps } from "./types";

export const getDefaultConfig = (
  theme: ThemeType = "light",
): DonutProps["config"] => {
  return {
    labelProps: {
      centerLabel: {
        text1: {
          text: "",
          fontSize: 24,
          fontWeight: "500",
          populateText: true,
          fillColor: "#000000",
          oversizedBehavior: "fit",
          textAlign: "center",
          centerX: 50,
          centerY: 50,
        },
        text2: {
          text: "",
          fontSize: 13,
          fontWeight: "500",
          populateText: true,
          fillColor: "#464646", //TODO no nimbus color
          oversizedBehavior: "fit",
          textAlign: "center",
          centerX: 50,
          centerY: 50,
        },
      },
      labelSetting: {
        fontSize: 10,
        fontWeight: "200",
      },
    },
    pieSetting: {
      strokeColor: datavizColors[theme].dataviz.neutrals.gaps,
      strokeWidth: 3,
    },
    chartProps: {
      chartSettings: { innerRadius: 75 },
    },
    tooltipProps: {
      showTooltip: false,
      tooltipSetting: {
        html: ``,
        pointerOrientation: "vertical",
      },
      tooltipBackgroundSetting: {},
    },

    legendProps: {
      showLegend: true,
      legendSetting: {
        nameField: "name",
        fillField: "color",
        strokeField: "color",
        centerX: 50,
        x: 50,
      },
      legendMarkerProps: {
        height: 8,
        width: 8,
      },
      legendLabelProps: {
        legendLabelColor: "#191919", //TODO no nimbus color
        fontSize: 13,
        fontWeight: "400",
      },
      legendRectangleProps: {
        cornerRadiusTL: 5,
        cornerRadiusTR: 5,
        cornerRadiusBL: 5,
        cornerRadiusBR: 5,
      },
    },
    emptyProp: {
      showEmpty: false,
      emptyText: "0",
      data: [
        {
          category: "a",
          count: 1,
          color: datavizColors[theme].dataviz.neutrals.others,
        },
      ],
    },
  };
};
