import { useLayoutEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import * as am5 from "@amcharts/amcharts5";
import * as am5percent from "@amcharts/amcharts5/percent";
import am5themes_Animated from "@amcharts/amcharts5/themes/Animated";
import { getMergedConfigs } from "../utils";
import { updateLegend } from "../utils/legendUtils";
import { getDefaultConfig } from "./DefaultConfig";
import { type DonutProps, type DonutDataType } from "./types";
import { getDatavizTheme } from "../common/datavizTheme";

export const Donut = (props: DonutProps) => {
  const { data, config, dataKeys, chartId, theme } = props;
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();

  const mergedConfigs = getMergedConfigs(getDefaultConfig(theme), config);
  const [value] = dataKeys;

  useLayoutEffect(() => {
    if (!chartContainerRef.current) return;

    const root = am5.Root.new(chartContainerRef.current, {
      tooltipContainerBounds: {
        top: -10,
        right: 100,
        bottom: 0,
        left: 100,
      },
    });

    root.setThemes([
      am5themes_Animated.new(root),
      getDatavizTheme(root, theme),
    ]);
    root.numberFormatter.setAll({
      ...mergedConfigs?.numberFormatter,
    });
    const { legendProps, emptyProp } = mergedConfigs ?? {};

    const chart = root.container.children.push(
      am5percent.PieChart.new(root, {
        layout:
          legendProps?.direction === "right"
            ? root.horizontalLayout
            : root.verticalLayout,
        ...mergedConfigs.chartProps?.chartSettings,
        ...(mergedConfigs.chartProps?.chartSettings?.innerRadius
          ? {
              innerRadius: am5.percent(
                mergedConfigs.chartProps?.chartSettings?.innerRadius,
              ),
            }
          : {}),
        radius: am5.p100,
      }),
    );

    chart.chartContainer.setAll({
      ...mergedConfigs.chartProps?.chartContainer,
    });

    const tooltip = am5.Tooltip.new(root, {
      background: am5.PointedRectangle.new(root, {
        ...mergedConfigs.tooltipProps?.tooltipBackgroundSetting,
      }),
      ...(emptyProp?.showEmpty ? { forceHidden: true } : {}),
      ...mergedConfigs.tooltipProps?.tooltipSetting,
    });

    const donut = chart.series.push(
      am5percent.PieSeries.new(root, {
        valueField: value,
        alignLabels: false,
      }),
    );

    const {
      fillColor,
      centerX: centerLabelX1,
      centerY: centerY1,
      text: text1,
    } = mergedConfigs.labelProps?.centerLabel?.text1 ?? {};

    const label = am5.Label.new(root, {
      dy: -15,
      ...mergedConfigs.labelProps?.centerLabel?.text1,
      text:
        emptyProp?.showEmpty && emptyProp.emptyText
          ? emptyProp.emptyText
          : t(text1 ?? ""),
      ...(fillColor
        ? {
            fill: am5.color(fillColor),
          }
        : {}),
      centerX: am5.percent(centerLabelX1!),
      centerY: am5.percent(centerY1!),
    });

    const {
      fillColor: fillColor2,
      centerX: centerLabelX,
      centerY,
      text: text2,
    } = mergedConfigs.labelProps?.centerLabel?.text2 ?? {};

    const label2 = am5.Label.new(root, {
      ...mergedConfigs.labelProps?.centerLabel?.text2,
      text: text2,
      ...(fillColor2
        ? {
            fill: am5.color(fillColor2),
          }
        : {}),
      centerX: am5.percent(centerLabelX!),
      centerY: am5.percent(centerY!),
      dy: 10,
    });

    donut.children.push(label);
    donut.children.push(label2);

    donut.events.on("boundschanged", function (ev) {
      if (ev.target.width() > 315) {
        label2.set("fontSize", mergedConfigs.labelProps?.fontLarge ?? 15);
      } else if (ev.target.width() > 225) {
        label2.set("fontSize", mergedConfigs.labelProps?.fontMedium ?? 10);
      } else {
        label2.set("fontSize", mergedConfigs.labelProps?.fontSmall ?? 9);
      }
    });

    donut.slices.template.setAll({
      ...mergedConfigs.pieSetting,
      ...(mergedConfigs.pieSetting?.strokeColor
        ? { stroke: am5.color(mergedConfigs.pieSetting.strokeColor) }
        : {}),
    });

    donut.slices.template.adapters.add("fill", (value, target) => {
      if (!target.dataItem?.dataContext) {
        return value;
      }

      const dataContext = target.dataItem.dataContext as DonutDataType;

      return dataContext.color ? am5.color(dataContext.color) : value;
    });

    donut.slices.template.states.create("hover", {
      scale: 1.05,
    });

    donut.slices.template.set("toggleKey", "none");

    donut.ticks.template.set("visible", false);

    donut.labels.template.setAll({
      ...mergedConfigs.labelProps?.labelSetting,
    });

    donut.slices.template.set("tooltip", tooltip);

    const donutPointerOverEvt = donut.slices.template.events.on(
      "pointerover",
      function (ev) {
        donut.slices.each(function (slice) {
          if (slice != ev.target) {
            slice.set("fillOpacity", 0.6);
          } else {
            slice.set("fillOpacity", undefined);
          }
        });
      },
      this,
    );

    const donutPointerOutEvt = donut.slices.template.events.on(
      "pointerout",
      function () {
        donut.slices.each(function (slice) {
          slice.set("fillOpacity", undefined);
        });
      },
      this,
    );

    if (emptyProp?.showEmpty && emptyProp.data) {
      donut.data.setAll(emptyProp.data);
    } else {
      donut.data.setAll(data);
    }

    if (mergedConfigs.legendProps?.showLegend && !emptyProp?.showEmpty) {
      const legend = chart.children.push(
        am5.Legend.new(root, {
          clickTarget: "none",
          ...mergedConfigs.legendProps.legendSetting,
          ...(legendProps?.direction === "right"
            ? {
                centerY: am5.p50,
                x: am5.percent(65),
                y: am5.p50,
                layout: root.verticalLayout,
              }
            : {
                centerX: am5.p50,
                x: am5.p50,
                ...(legendProps?.gridLayout
                  ? {
                      layout: am5.GridLayout.new(root, {}),
                    }
                  : {}),
              }),
        }),
      );

      if (legendProps) {
        updateLegend({ root, legendsProps: legendProps }, legend);
      }

      legend.data.setAll(donut.dataItems);
    }

    donut.appear(1000, 100).catch((e) => {
      throw e;
    });

    return () => {
      root.dispose();
      donutPointerOutEvt.dispose();
      donutPointerOverEvt.dispose();
    };
  }, [mergedConfigs, data, value, t]);

  return (
    <div
      data-testid={`donut-chart-${chartId}`}
      className={mergedConfigs.containerClass}
      ref={chartContainerRef}
    />
  );
};
