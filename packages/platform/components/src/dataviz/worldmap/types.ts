import type * as am5 from "@amcharts/amcharts5";
import type * as am5map from "@amcharts/amcharts5/map";
import { type IClusteredPointSeriesSettings } from "@amcharts/amcharts5/map";

import { type DonutConfig } from "../donut/types";
import {
  type ThemeType,
  type ChartProps,
  type CommonTooltipData,
  type WorldMapZoomControlProps,
  DatavizColorType,
  DatavizTooltipProps,
} from "../common/types";

export type WorldMapZoomSettings = {
  geoPoint: am5.IGeoPoint;
  level: number;
};

export type WorldMapValuesType = {
  value: number;
  key?: string;
  color: string;
  type: string;
  icon?: string;
  showRippleAnimation?: boolean;
};

export type WorldMapDataProp = {
  label: string;
  type?: string;
  latitude: number;
  longitude: number;
  country?: string;

  icon?: string;
  color?: string;

  tooltipData?: CommonTooltipData[];
  values?: WorldMapValuesType[];
  bulletData?: WorldMapValuesType[];

  strokeColor?: string;
  backgroundColor?: string;
  // strokeColor?: string;
  // backgroundColor?: string;
  // showRippleAnimation?: boolean;
};

export type WorldMapClusterConfig = {
  clusterPointColor?: string;
  clusterLabelColor?: string;
  clusterContainerConfig?: am5.IContainerSettings;
  clusterCircleConfig?: am5.ICircleSettings;
  clusterLabelConfig?: am5.ILabelSettings & { fillColor?: string };
  clusterSetting?: IClusteredPointSeriesSettings;
  onClusterClick?: (d: WorldMapValuesType[]) => void;
  tooltipProps?: DatavizTooltipProps;
};

export type BulletType = "bubble" | "icon" | "donut";

export type WorldMapBulletConfig = {
  bulletContainerConfig?: am5.IContainerSettings;
  bulletCircleConfig?: am5.ICircleSettings;
  bulletLabelConfig?: am5.ILabelSettings;
  bulletBackgroundColor?: string;
  bulletStrokeColor?: string;
  bulletType?: BulletType;
  onBulletClick?: (d: WorldMapValuesType[]) => void;
};

export type DefaultMapColors = {
  mapBackgroundColor: string;
  mapStrokeColor: string;
  mapRegionColor: string;
  bulletBackgroundColor: string;
  bulletStrokeColor: string;
  tooltipBackgroundColor: string;
  tooltipShadowColor: string;
  tooltipStrokeColor: string;
  zoomControlColor: string;
};

export type MapColors = {
  mapBackgroundColor?: string;
  mapStrokeColor?: string;
  mapRegionColor?: string;
  bulletBackgroundColor?: string;
  bulletStrokeColor?: string;
  tooltipBackgroundColor?: string;
  tooltipShadowColor?: string;
  zoomControlColor?: string;
};

export type PolygonType = am5.IPolygonSettings & DatavizColorType;

export type WorldMapConfig = {
  containerClass: string;
  mapConfig: Omit<am5.IGraphicsSettings, "fill">;
  mapPolygonConfig?: PolygonType;
  clusterConfig: WorldMapClusterConfig;
  bulletConfig: WorldMapBulletConfig;
  tooltipProps: DatavizTooltipProps;
  mapColors: Partial<DefaultMapColors>;
  zoomControlProps?: WorldMapZoomControlProps;
  defaultZoomSetting?: WorldMapZoomSettings;
  chartProps?: ChartProps;
  pieProps?: DonutConfig;
};

export type WorldMapMetaType = {
  color: string;
  label: string;
  heading: string;
  icon: string;
  showTotalValueInDonut?: boolean;
};

export type WorldMapMetaConfigType = {
  meta: WorldMapMetaType;
  data: Record<string, WorldMapMetaType>;
};

export type WorldMapProps = {
  customConfig?: Partial<WorldMapConfig>;
  data: WorldMapDataProp[];
  chartId?: string;
  metaDataConfig: WorldMapMetaConfigType;
  onBulletClick?: (d: WorldMapValuesType[]) => void;
  handleShowState?: (d: boolean) => void;
  multiZoomLevelGroup?: {
    seriesTypes: string[];
    zoomLevel: number;
  };
  theme: ThemeType;
  setMapRef?: (d: am5map.MapChart, root: am5.Root) => void;
};
