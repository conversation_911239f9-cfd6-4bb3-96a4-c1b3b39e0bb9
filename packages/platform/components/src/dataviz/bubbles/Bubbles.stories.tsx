import type { <PERSON>a, StoryObj } from "@storybook/react";
import { Card } from "@zs-nimbus/core";
import { bubblesData2 } from "./Bubbles.Data";
import Bubbles from "./Bubbles";

const meta = {
  title: "dataviz",
  component: Bubbles,
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/wLxNEe788V2qdOKQDy31Of/CXO---Desktop-(Analytics)?type=design&node-id=2434-55252&mode=design&t=deBeYFA7WP3acawi-0",
    },
  },
} as Meta<typeof Bubbles>;

export default meta;
type Story = StoryObj<typeof meta>;

export const BubblesChart: Story = {
  decorators: [(Story) => <Story />],
};

BubblesChart.decorators = [
  () => (
    <div className="mx-l">
      <Card className="mt-rem-160">
        <Card.Header>Hierarchy Force directed bubbles chart</Card.Header>
        <div>
          <Bubbles
            theme="light"
            data={bubblesData2}
            config={{
              containerClass: "flex h-[550px]",
            }}
          />
        </div>
      </Card>
    </div>
  ),
];
