import type * as am5hierarchy from "@amcharts/amcharts5/hierarchy";
import type * as am5 from "@amcharts/amcharts5";
import { DatavizTooltipProps, ThemeType } from "../common/types";

export type BubbleProps = am5hierarchy.IForceDirectedSettings;

export type BubbleData = {
  value: number;
  children: BubbleChildrenData[];
};

export type BubbleChildrenData = {
  name: string;
  value?: string | number;
  fillColor?: string;
  percentage?: string;
};

export type BubbleLabelProps = am5.ILabelSettings;

export type BubblesConfigProps = {
  containerClass?: string;
  bubbleProps?: BubbleProps;
  bubbleLabelProps?: BubbleLabelProps;
  zoomContainer?: am5.IContainerSettings;
  tooltipProps?: DatavizTooltipProps;
  numberFormatter?: am5.INumberFormatterSettings;
};

export type BubblesProps = {
  data: BubbleData[];
  config: BubblesConfigProps;
  chartId?: string;
  theme: ThemeType;
};
