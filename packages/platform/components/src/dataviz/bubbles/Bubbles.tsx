import { useLayoutEffect, useMemo, useRef } from "react";
import * as am5 from "@amcharts/amcharts5";
import am5themes_Animated from "@amcharts/amcharts5/themes/Animated";
import * as am5hierarchy from "@amcharts/amcharts5/hierarchy";
import { type INumberFormatterSettings } from "@amcharts/amcharts5";
import { BubblesDefaultConfig } from "./BubblesDefaultConfig";
import { type BubblesProps } from "./types";
import { getMergedConfigs } from "../utils";
import { getDatavizTheme } from "../hooks";

const Bubbles = ({ data, config, chartId, theme }: BubblesProps) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const mergedConfig = useMemo(
    () => getMergedConfigs(BubblesDefaultConfig, config),
    [config],
  );

  useLayoutEffect(() => {
    if (!chartContainerRef.current || !data) return;

    const root = am5.Root.new(chartContainerRef.current);

    root.setThemes([
      am5themes_Animated.new(root),
      getDatavizTheme(root, theme),
    ]);

    root.numberFormatter.setAll({
      ...(mergedConfig.numberFormatter as Partial<INumberFormatterSettings>),
    });

    const container = root.container.children.push(
      am5.Container.new(root, {
        draggable: false,
        ...mergedConfig?.zoomContainer,
      }),
    );

    const series = container.children.push(
      am5hierarchy.ForceDirected.new(root, {
        initialFrames: 50,
        velocityDecay: 0.3,
        ...mergedConfig?.bubbleProps,
      }),
    );

    series.labels.template.setAll({
      ...mergedConfig.bubbleLabelProps,
    });

    const { tooltipBackgroundSetting, tooltipSetting, tooltipHTML } =
      mergedConfig?.tooltipProps ?? {};

    const tooltip = am5.Tooltip.new(root, {
      ...tooltipSetting,
    });

    tooltip.get("background")?.setAll({
      ...tooltipBackgroundSetting,
      ...(tooltipBackgroundSetting?.fillColor
        ? { fill: am5.color(tooltipBackgroundSetting?.fillColor ?? "") }
        : {}),
      ...(tooltipBackgroundSetting?.shadowFillColor
        ? { shadowColor: am5.color(tooltipBackgroundSetting?.shadowFillColor) }
        : {}),
    });

    series.nodes.template.setAll({
      draggable: false,
    });

    series.nodes.template.set("tooltip", tooltip);

    series.circles.template.setAll({
      tooltipY: am5.p50,
      tooltipX: am5.p50,
      tooltipHTML: tooltipHTML,
    });

    series.circles.template.adapters.add("fill", function (fill, target) {
      const { fillColor } = target.dataItem?.dataContext as {
        fillColor: string;
      };

      return fillColor ? am5.color(fillColor) : fill;
    });

    series.data.setAll(data);
    void series.appear(1000, 100);

    return () => {
      root.dispose();
    };
  }, [mergedConfig, data]);

  return (
    <div
      data-testid={`${chartId}-chart`}
      className={mergedConfig?.containerClass}
      ref={chartContainerRef}
    />
  );
};

export default Bubbles;
