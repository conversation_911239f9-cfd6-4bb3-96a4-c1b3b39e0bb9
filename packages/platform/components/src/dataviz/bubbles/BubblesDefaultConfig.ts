import * as am5 from "@amcharts/amcharts5";
import { type BubblesConfigProps } from "./types";

export const BubblesDefaultConfig: BubblesConfigProps = {
  containerClass: "min-h-[550px]",
  bubbleProps: {
    topDepth: 1,
    maxRadius: 60,
    minRadius: 20,
    valueField: "value",
    categoryField: "name",
    childDataField: "children",
  },
  bubbleLabelProps: {
    fontSize: 14,
    oversizedBehavior: "truncate",
  },
  zoomContainer: {
    width: am5.p100,
    height: am5.p100,
    wheelable: true,
  },
  tooltipProps: {
    tooltipBackgroundSetting: {
      fillOpacity: 1,
    },
    tooltipSetting: {
      getFillFromSprite: false,
      autoTextColor: false,
    },
    tooltipHTML: `<h3>{name}: {value}</h3>`,
  },
};
