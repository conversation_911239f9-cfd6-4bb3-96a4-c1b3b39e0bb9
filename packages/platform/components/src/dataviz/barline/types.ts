import {
  type IGraphicsSettings,
  type IRoundedRectangleSettings,
} from "@amcharts/amcharts5";
import { type IXYAxis } from "@amcharts/amcharts5/.internal/charts/xy/series/XYSeries";
import {
  type IColumnSeriesSettings,
  type ISmoothedXLineSeriesSettings,
} from "@amcharts/amcharts5/xy";
import {
  ThemeType,
  type AxisProps,
  type DatavizTooltipProps,
} from "../common/types";

export type BarLineDataContext = {
  category: string;
  yes: number;
  no?: number;
};

type LineSeriesSettings = Omit<
  ISmoothedXLineSeriesSettings,
  "xAxis" | "yAxis" | "stroke"
> & {
  xAxis?: IXYAxis;
  yAxis?: IXYAxis;
  strokeColor?: string;
};

type LineSeriesStrokeSettings = Omit<IGraphicsSettings, "shadowColor"> & {
  shadowFillColor?: string;
};

type LineSeriesTemplateSettings = Omit<IGraphicsSettings, "fillGradient"> & {
  fillGradientProp: [string, string];
};

type LineSeriesProps = {
  lineSeriesSettings?: LineSeriesSettings;
  lineSeriesStrokeSettings?: LineSeriesStrokeSettings;
  lineSeriesTemplateSettings?: LineSeriesTemplateSettings;
};

type ColumnSeriesSettings = Omit<IColumnSeriesSettings, "xAxis" | "yAxis"> & {
  xAxis?: IXYAxis;
  yAxis?: IXYAxis;
};

type ColumnSeriesTemplateSettings = Omit<
  IRoundedRectangleSettings,
  "fillGradient"
> & {
  fillGradientProp?: [string, string];
};

type ColumnSeriesProps = {
  columnSeriesSettings?: ColumnSeriesSettings;
  columnSeriesTemplateSettings?: ColumnSeriesTemplateSettings;
};

type StackedColumnSeriesSettings = ColumnSeriesSettings;

type StackedColumnSeriesTemplateSettings = Omit<
  IRoundedRectangleSettings,
  "fill"
> & {
  fillColor?: string;
};

type StackedColumnSeriesProps = {
  stackedColumnSeriesSettings?: StackedColumnSeriesSettings;
  stackedColumnSeriesTemplateSettings?: StackedColumnSeriesTemplateSettings;
};

export type BarLineProps = {
  data: BarLineDataContext[];
  configs?: {
    containerClass?: string;
    tooltipProps?: DatavizTooltipProps;
    axisProps?: AxisProps;
    lineSeriesProps?: LineSeriesProps;
    columnSeriesProps?: ColumnSeriesProps;
    stackedColumnSeriesProps?: StackedColumnSeriesProps;
  };
  chartId?: string;
  theme: ThemeType;
};
