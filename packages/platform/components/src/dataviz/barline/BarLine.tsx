import { useLayoutEffect, useRef } from "react";
import * as am5 from "@amcharts/amcharts5";
import * as am5xy from "@amcharts/amcharts5/xy";
import am5themes_Animated from "@amcharts/amcharts5/themes/Animated";
import { useTranslation } from "react-i18next";
import { getMergedConfigs } from "../utils";
import { getDatavizTheme } from "../common/datavizTheme";
import { getStackedColumnTooltipHtml } from "../utils/tooltips";
import { type BarLineDataContext, type BarLineProps } from "./types";
import { getDefaultConfigs } from "./BarLineDefaultConfig";

const BarLine = ({ data, configs, chartId, theme }: BarLineProps) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const mergedConfigs = getMergedConfigs(getDefaultConfigs(theme), configs);
  const { t } = useTranslation();

  useLayoutEffect(() => {
    if (!chartContainerRef.current) return;
    const root = am5.Root.new(chartContainerRef.current);

    root.setThemes([am5themes_Animated.new(root), getDatavizTheme(root,theme)]);

    const chart = root.container.children.push(am5xy.XYChart.new(root, {}));

    chart.gridContainer.toFront();

    const {
      xAxisSetting,
      yAxisSetting,
      yAxisProps,
      yGridProps,
      xLabelProps,
      yLabelProps,
      yAxisTitleSetting,
    } = mergedConfigs?.axisProps ?? {};

    const { tooltipSetting, tooltipBackgroundSetting } =
      mergedConfigs?.tooltipProps ?? {};

    const tooltip = am5.Tooltip.new(root, {
      ...tooltipSetting,
    });

    tooltip.get("background")?.setAll({
      ...(tooltipBackgroundSetting?.shadowFillColor
        ? { shadowColor: am5.color(tooltipBackgroundSetting?.shadowFillColor) }
        : {}),
      ...(tooltipBackgroundSetting?.strokeColor
        ? { stroke: am5.color(tooltipBackgroundSetting?.strokeColor) }
        : {}),
      ...(tooltipBackgroundSetting?.fillColor
        ? { fill: am5.color(tooltipBackgroundSetting?.fillColor) }
        : {}),
      ...tooltipBackgroundSetting,
    });

    const xAxis = chart.xAxes.push(
      am5xy.CategoryAxis.new(root, {
        categoryField: xAxisSetting!.categoryField!,
        renderer: am5xy.AxisRendererX.new(root, {}),
        ...xAxisSetting,
      }),
    );

    const xAxisRenderer = xAxis.get("renderer");
    xAxisRenderer.grid.template.set("forceHidden", true);
    xAxisRenderer.labels.template.setAll({
      ...(xLabelProps?.labelColor
        ? { fill: am5.color(xLabelProps?.labelColor) }
        : {}),
      ...xLabelProps,
    });

    xAxis.data.setAll(data);

    const yRenderer = am5xy.AxisRendererY.new(root, {
      ...yAxisProps,
    });

    const yAxis = chart.yAxes.push(
      am5xy.ValueAxis.new(root, {
        renderer: yRenderer,
        ...yAxisSetting!,
      }),
    );

    if (yLabelProps?.showCustomLabel) {
      yAxis
        ?.get("renderer")
        ?.labels.template.adapters.add("text", function (text, target) {
          if (yLabelProps.labelFormatter) {
            return yLabelProps?.labelFormatter?.(text);
          }
          if (target.dataItem?.dataContext) {
            const { yLabel } = target.dataItem.dataContext as {
              yLabel: string;
            };

            return yLabel;
          }

          return text;
        });
    }

    const yAxisRenderer = yAxis.get("renderer");
    yAxisRenderer.grid.template.setAll({
      ...yGridProps,
    });
    yAxisRenderer.labels.template.setAll({
      ...(yLabelProps?.labelColor
        ? { fill: am5.color(yLabelProps?.labelColor) }
        : {}),
      ...yLabelProps,
    });

    yAxis.children.unshift(
      am5.Label.new(root, {
        ...(yAxisTitleSetting?.text
          ? { text: t(yAxisTitleSetting?.text) }
          : {}),
        ...(yAxisTitleSetting?.labelColor
          ? { fill: am5.color(yAxisTitleSetting?.labelColor) }
          : {}),
        ...yAxisTitleSetting,
      }),
    );

    const {
      lineSeriesSettings,
      lineSeriesStrokeSettings,
      lineSeriesTemplateSettings,
    } = mergedConfigs?.lineSeriesProps ?? {};

    const { columnSeriesSettings, columnSeriesTemplateSettings } =
      mergedConfigs?.columnSeriesProps ?? {};

    const { stackedColumnSeriesSettings, stackedColumnSeriesTemplateSettings } =
      mergedConfigs?.stackedColumnSeriesProps ?? {};

    function createLineSeries() {
      const series = chart.series.push(
        am5xy.SmoothedXLineSeries.new(root, {
          xAxis: xAxis,
          yAxis: yAxis,
          ...(lineSeriesSettings?.strokeColor
            ? { stroke: am5.color(lineSeriesSettings?.strokeColor) }
            : {}),
          ...lineSeriesSettings,
        }),
      );

      series.strokes.template.setAll({
        ...(lineSeriesStrokeSettings?.shadowFillColor
          ? {
              shadowColor: am5.color(lineSeriesStrokeSettings?.shadowFillColor),
            }
          : {}),
        ...lineSeriesStrokeSettings,
      });

      series.fills.template.setAll({
        ...(lineSeriesTemplateSettings?.fillGradientProp?.length
          ? {
              fillGradient: am5.LinearGradient.new(root, {
                stops: [
                  {
                    color: am5.color(
                      lineSeriesTemplateSettings.fillGradientProp[0],
                    ),
                  },
                  {
                    color: am5.color(
                      lineSeriesTemplateSettings.fillGradientProp[1],
                    ),
                  },
                ],
              }),
            }
          : {}),
        ...lineSeriesTemplateSettings,
      });

      series.data.setAll(data);
      void series.appear(1000);
    }

    function createColumnSeries() {
      const series = chart.series.push(
        am5xy.ColumnSeries.new(root, {
          xAxis: xAxis,
          yAxis: yAxis,
          ...columnSeriesSettings,
        }),
      );

      series.columns.template.setAll({
        ...(columnSeriesTemplateSettings?.fillGradientProp?.length
          ? {
              fillGradient: am5.LinearGradient.new(root, {
                stops: [
                  {
                    color: am5.color(
                      columnSeriesTemplateSettings.fillGradientProp[0],
                    ),
                  },
                  {
                    color: am5.color(
                      columnSeriesTemplateSettings.fillGradientProp[1],
                    ),
                  },
                ],
              }),
            }
          : {}),
        ...columnSeriesTemplateSettings,
      });

      function roundRadius(_: number | undefined, item: am5.RoundedRectangle) {
        if (item.dataItem?.dataContext) {
          const { dataContext, component } = item.dataItem;
          let lastSeries;
          const { valueYField } = series?._settings as { valueYField: string };
          const dataContextTyped = dataContext as Record<string, unknown>;
          if (stackedColumnSeriesSettings?.valueYField) {
            if (
              dataContextTyped[
                stackedColumnSeriesSettings?.valueYField ?? valueYField
              ]
            ) {
              lastSeries = series;
            }
          }

          return component != lastSeries ? 5 : 0;
        }
      }

      series.columns.template.adapters.add("tooltipHTML", function (_, target) {
        if (target.dataItem) {
          const context = target.dataItem?.dataContext as Record<
            string,
            number | string
          >;

          return getStackedColumnTooltipHtml?.(context as BarLineDataContext);
        }
      });

      series.columns.template.set("tooltip", tooltip);

      series.columns?.template?.adapters.add("cornerRadiusTL", roundRadius);
      series.columns?.template?.adapters.add("cornerRadiusTR", roundRadius);

      series.data.setAll(data);
      void series.appear(1000);
    }

    function createStackedColumnSeries() {
      const series = chart.series.push(
        am5xy.ColumnSeries.new(root, {
          xAxis: xAxis,
          yAxis: yAxis,
          ...stackedColumnSeriesSettings,
        }),
      );

      series.columns.template.setAll({
        ...(stackedColumnSeriesTemplateSettings?.fillColor
          ? { fill: am5.color(stackedColumnSeriesTemplateSettings?.fillColor) }
          : {}),
        ...stackedColumnSeriesTemplateSettings,
      });

      series.columns.template.adapters.add("tooltipHTML", function (_, target) {
        if (target.dataItem) {
          const context = target.dataItem?.dataContext as Record<
            string,
            number | string
          >;

          return getStackedColumnTooltipHtml?.(context as BarLineDataContext);
        }
      });

      series.columns.template.set("tooltip", tooltip);

      series.data.setAll(data);
      void series.appear(1000);
    }

    createLineSeries();
    createColumnSeries();
    createStackedColumnSeries();

    void chart.appear(1000, 100);

    return () => {
      root.dispose();
    };
  }, [data, mergedConfigs, t]);

  return (
    <div
      className={mergedConfigs?.containerClass}
      data-testid={chartId}
      ref={chartContainerRef}
    />
  );
};

export default BarLine;
