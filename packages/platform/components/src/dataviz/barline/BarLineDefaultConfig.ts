import * as am5 from "@amcharts/amcharts5";
import { type BarLineProps } from "./types";
import { ThemeType } from "../common/types";
import { datavizColors } from "@zs-nimbus/dataviz-colors";

export const getDefaultConfigs = (
  theme: ThemeType,
): BarLineProps["configs"] => {
  return {
    containerClass: "flex h-[550px]",
    tooltipProps: {
      tooltipSetting: {
        pointerOrientation: "horizontal",
      },
    },
    axisProps: {
      xAxisSetting: {
        categoryField: "category",
        startLocation: 0,
        endLocation: 1,
      },
      yAxisSetting: {
        min: 0,
        numberFormat: "#0a",
      },
      yAxisProps: {
        minGridDistance: 100,
      },
      yGridProps: {
        strokeWidth: 1,
        visible: true,
      },
      yLabelProps: {
        fontSize: 12,
        fontWeight: "400",
      },
      yAxisTitleSetting: {
        rotation: -90,
        text: "TOTAL_TRAFFIC",
        fontSize: 12,
        fontWeight: "400",
        y: am5.percent(50),
        centerX: am5.percent(50),
      },
    },
    lineSeriesProps: {
      lineSeriesSettings: {
        valueYField: "yes",
        categoryXField: "category",
        strokeColor: datavizColors[theme].dataviz.neutrals.gaps,
      },
      lineSeriesStrokeSettings: {
        shadowFillColor: datavizColors[theme].dataviz.sequential.blue.scale03,
      },
      lineSeriesTemplateSettings: {
        shadowBlur: 2,
        shadowOffsetX: 2,
        shadowOffsetY: 2,
        shadowOpacity: 0.1,
        fillOpacity: 1,
        visible: true,
        fillGradientProp: [
          "#DDEAFF",
          datavizColors[theme].dataviz.neutrals.gaps,
        ],
      },
    },
    columnSeriesProps: {
      columnSeriesSettings: {
        valueYField: "yes",
        categoryXField: "category",
        stacked: true,
      },
      columnSeriesTemplateSettings: {
        width: 38,
        fillGradientProp: [
          "#4B7EEC",
          datavizColors[theme].dataviz.categorical.primary.chart01,
        ], //TODO "#4B7EEC", nimbus equivalent not found
        cornerRadiusTL: 4,
        cornerRadiusTR: 4,
      },
    },
    stackedColumnSeriesProps: {
      stackedColumnSeriesSettings: {
        valueYField: "no",
        categoryXField: "category",
        stacked: true,
      },
      stackedColumnSeriesTemplateSettings: {
        width: 38,
        fillColor: datavizColors[theme].dataviz.sequential.blue.scale05,
        cornerRadiusTL: 4,
        cornerRadiusTR: 4,
      },
    },
  };
};
