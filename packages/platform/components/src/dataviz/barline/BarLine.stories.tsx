import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import * as am5 from "@amcharts/amcharts5";
import { barLineData } from "./mock.data";
import component from "./BarLine";

const meta: Meta<typeof component> = {
  title: "dataviz",
  component,
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/wLxNEe788V2qdOKQDy31Of/CXO---Desktop-(Analytics)?type=design&node-id=9960-49626",
    },
  },
};

export default meta;

export const BarLine: StoryObj<typeof meta> = {
  args: {
    data: barLineData,
    theme: "light",
    configs: {
      containerClass: "flex h-[550px]",
      tooltipProps: {
        tooltipSetting: {
          getFillFromSprite: false,
          autoTextColor: false,
          paddingBottom: 8,
          paddingLeft: 8,
          paddingRight: 8,
          paddingTop: 8,
        },
        tooltipBackgroundSetting: {
          fillColor: "#FFFFFF",
          shadowFillColor: "#191919",
          strokeColor: "#FFFFFF",
        },
      },
      axisProps: {
        xAxisSetting: {
          categoryField: "category",
          startLocation: 0,
          endLocation: 1,
        },
        xLabelProps: {
          fontSize: 14,
          fontWeight: "400",
        },
        yAxisSetting: {
          min: 0,
          numberFormat: "#0a",
        },
        yAxisProps: {
          minGridDistance: 100,
        },
        yLabelProps: {
          labelColor: "#767676",
          fontSize: 12,
          fontWeight: "400",
        },
        yAxisTitleSetting: {
          rotation: -90,
          text: "Total Traffic",
          labelColor: "#767676",
          fontSize: 12,
          fontWeight: "400",
          y: am5.percent(50),
          centerX: am5.percent(50),
        },
      },
      lineSeriesProps: {
        lineSeriesSettings: {
          valueYField: "yes",
          categoryXField: "category",
          strokeColor: "#FFFFFF",
        },
        lineSeriesStrokeSettings: {
          shadowBlur: 2,
          shadowOffsetX: 2,
          shadowOffsetY: 2,
          shadowFillColor: "#2160E1",
          shadowOpacity: 0.1,
        },
        lineSeriesTemplateSettings: {
          fillOpacity: 1,
          visible: true,
          fillGradientProp: ["#DDEAFF", "#FFFFFF"],
        },
      },
      columnSeriesProps: {
        columnSeriesSettings: {
          valueYField: "yes",
          categoryXField: "category",
          stacked: true,
        },
        columnSeriesTemplateSettings: {
          width: 38,
          fillGradientProp: ["#4B7EEC", "#194CBB"],
        },
      },
      stackedColumnSeriesProps: {
        stackedColumnSeriesSettings: {
          valueYField: "no",
          categoryXField: "category",
          stacked: true,
        },
        stackedColumnSeriesTemplateSettings: {
          width: 38,
          fillColor: "#072F84",
          tooltipY: 0,
          tooltipHTML: `<div class="flex flex-col bg-semantic-surface-elevated-low10 px-[20px] py-[8px] leading-5">
            <div class="font-medium text-[13px] text-grey-900 mb-[4px]">SSL/TLS inspection</div>
            <div class="flex font-normal text-[13px] justify-between">
              <span class="text-grey-900">yes</span> 
              <span class="text-grey-900">{yes}</span>
            </div>
            <div class="flex font-normal text-[13px] justify-between border-solid border-b-grey-100">
              <span class="text-grey-900">no</span> 
              <span class="text-grey-900">{no}</span>
            </div>
            <div class="font-normal text-[13px]">
              <span class="text-grey-500">Very Little traffic inspected</span>
            </div>
        </div>`,
        },
      },
    },
  },
};
