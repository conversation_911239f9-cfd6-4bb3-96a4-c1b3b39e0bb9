import { useLayoutEffect, useMemo, useRef } from "react";
import * as am5 from "@amcharts/amcharts5";
import * as am5hierarchy from "@amcharts/amcharts5/hierarchy";
import am5themes_Animated from "@amcharts/amcharts5/themes/Animated";
import { type IComponentDataItem } from "@amcharts/amcharts5/.internal/core/render/Component";
import { getMergedConfigs } from "../utils";
import { getDatavizTheme } from "../common/datavizTheme";
import { type TreeMapDataContext, type TreemapProps } from "./types";
import { getDefaultConfig } from "./TreemapDefaultConfig";

const Treemap = ({ data, config, chartId, theme }: TreemapProps) => {
  const treeMapContainerRef = useRef<HTMLDivElement>(null);
  const mergedConfigs = useMemo(
    () => getMergedConfigs(getDefaultConfig(theme), config),
    [theme, config],
  );
  useLayoutEffect(() => {
    if (!treeMapContainerRef.current) return;

    const root = am5.Root.new(treeMapContainerRef.current);

    root.setThemes([
      am5themes_Animated.new(root),
      getDatavizTheme(root, theme),
    ]);

    const {
      numberFormatter,
      treeMap,
      tooltipProps,
      template,
      nodes,
      legendProps,
    } = mergedConfigs ?? {};

    root.numberFormatter.setAll({
      ...numberFormatter,
    });

    const container = root.container.children.push(
      am5.Container.new(root, {
        width: am5.percent(100),
        height: am5.percent(100),
        layout: root.verticalLayout,
      }),
    );

    const series = container.children.push(
      am5hierarchy.Treemap.new(root, {
        ...treeMap,
      }),
    );
    const { tooltipSetting, tooltipBackgroundSetting } = tooltipProps ?? {};

    const tooltip = am5.Tooltip.new(root, {
      ...tooltipSetting,
    });

    tooltip.get("background")?.setAll({
      ...(tooltipBackgroundSetting?.fillColor
        ? { fill: am5.color(tooltipBackgroundSetting?.fillColor) }
        : {}),
      ...(tooltipBackgroundSetting?.shadowFillColor
        ? { shadowColor: am5.color(tooltipBackgroundSetting?.shadowFillColor) }
        : {}),
      ...tooltipProps?.tooltipBackgroundSetting,
    });

    series.rectangles.template.setAll({
      ...(template?.tooltipX
        ? { tooltipX: am5.percent(template?.tooltipX) }
        : {}),
      ...(template?.tooltipY
        ? { tooltipY: am5.percent(template?.tooltipY) }
        : {}),
      ...template,
    });

    series.rectangles.template.adapters.add("fillGradient", (value, target) => {
      if (!target.dataItem?.dataContext) {
        return value;
      }

      const dataContext = target.dataItem.dataContext as TreeMapDataContext;
      const primaryColor = dataContext.primaryColor;
      const secondaryColor = dataContext.secondaryColor;

      if (!primaryColor || !secondaryColor) return value;

      return am5.LinearGradient.new(root, {
        stops: [
          {
            color: am5.color(primaryColor),
          },
          {
            color: am5.color(secondaryColor),
          },
        ],
        rotation: 90,
      });
    });

    series.rectangles.template.setAll({
      tooltipX: am5.percent(50),
      tooltipY: am5.percent(40),
      tooltipHTML: nodes?.tooltipHTML,
    });

    series.nodes.template.set("tooltip", tooltip);

    series.data.setAll([data]);
    series.set("selectedDataItem", series.dataItems[0]);

    const { legendSetting, legendMarkerProps, legendLabelProps } =
      legendProps ?? {};

    const legend = container.children.unshift(
      am5.Legend.new(root, {
        ...legendSetting,
      }),
    );

    legend.markers.template.setAll({
      ...legendMarkerProps,
    });

    legend.labels.template.setAll({
      ...legendLabelProps,
    });

    const getPrimaryColor = (
      value: am5.Color | undefined,
      target: am5.RoundedRectangle,
    ): am5.Color | undefined => {
      const dataItem = target?.dataItem
        ?.dataContext as am5.DataItem<IComponentDataItem>;
      const { primaryColor } = dataItem?.dataContext as TreeMapDataContext;

      return primaryColor ? am5.color(primaryColor) : value;
    };

    legend.markerRectangles.template.adapters.add("fill", getPrimaryColor);
    legend.markerRectangles.template.adapters.add("stroke", getPrimaryColor);

    if (series?.dataItems?.[0])
      legend.data.setAll(series.dataItems[0].get("children"));

    legend.valueLabels.template.set("forceHidden", true);

    void series.appear(1000, 100);

    return () => root.dispose();
  }, [mergedConfigs, data]);

  return (
    <div
      className={mergedConfigs?.containerClass}
      data-testid={chartId}
      ref={treeMapContainerRef}
    />
  );
};

export default Treemap;
