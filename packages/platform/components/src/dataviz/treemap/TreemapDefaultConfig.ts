import { colors } from "@zs-nimbus/foundations";
import { type ThemeType } from "../common/types";
import { type TreemapProps } from "./types";

export const getDefaultConfig = (theme: ThemeType): TreemapProps["config"] => {
  return {
    containerClass: "flex h-[550px]",
    treeMap: {
      singleBranchOnly: false,
      initialDepth: 1,
      upDepth: -1,
      valueField: "value",
      childDataField: "children",
      legendLabelText: "{name}",
      nodePaddingOuter: 0,
      nodePaddingInner: 5,
    },
    template: {
      strokeWidth: 2,
      cornerRadiusTL: 8,
      cornerRadiusTR: 8,
      cornerRadiusBL: 8,
      cornerRadiusBR: 8,
      tooltipX: 90,
      tooltipY: 50,
    },
    nodes: {
      tooltipHTML: `<div class="text-semantic-content-base-primary font-normal">
        <div class="font-medium max-w-sm">{name}</div>
        <div class="typography-paragraph1 flex gap-xl"><span>Incidents</span> <span class="text-semantic-content-base-tertiary">{value}</span></div>
      </div>`,
    },
    legendProps: {
      legendSetting: { clickTarget: "none", paddingBottom: 12 },
      legendMarkerProps: { width: 10, height: 10 },
      legendLabelProps: {
        fontSize: 12,
        fontWeight: "400",
        maxWidth: 100,
        oversizedBehavior: "truncate",
      },
    },
  };
};
