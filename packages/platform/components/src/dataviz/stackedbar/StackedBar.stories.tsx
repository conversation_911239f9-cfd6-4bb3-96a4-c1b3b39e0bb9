import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import component from "./StackedBar";

const meta: Meta<typeof component> = {
  title: "dataviz",
  component,
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/wLxNEe788V2qdOKQDy31Of/CXO---Desktop-(Analytics)?type=design&node-id=2434-52686&mode=design&t=lmQg9xW4orYlPDsW-0",
    },
  },
};

export default meta;

export const StackedBarChart: StoryObj<typeof meta> = {
  name: "StackedBarChart",
  args: {
    theme: "light",
    data: {
      chartData: [
        {
          id: "notifications",
          lowMemory: 523,
          highCPU: 300,
          wifiIssues: 177,
          total: 523 + 300 + 177,
        },
      ],
      seriesData: [
        {
          label: "Low Memory",
          propertyName: "lowMemory",
          colorCode: "rgba(25, 76, 187, 1)",
        },
        {
          label: "High CPU",
          propertyName: "highCPU",
          colorCode: "rgba(37, 186, 226, 1)",
        },
        {
          label: "Wifi Issues",
          propertyName: "wifiIssues",
          colorCode: "rgba(159, 70, 215, 1)",
        },
      ],
    },
    dataKey: "id",
    customConfig: {
      stackedBarProps: {
        yGridProps: { visible: false },
        yAxisProps: {
          visible: false,
          marginLeft: 20,
        },
        xAxisProps: { min: 0, maxPrecision: 0 },
        xAxisLabelProps: {
          fontSize: 12,
        },
        tooltipProps: {
          tooltipGraphicsConfig: {
            shadowBlur: 10,
            strokeWidth: 1,
          },
          tooltipSettingsConfig: {
            pointerOrientation: "up",
          },
          tooltipHTML: `<div>
                    <div class="font-bold text-[14px] text-grey-900 mb-[4px]">{name} Notifications</div>
                    <div class="flex justify-between font-normal text-[13px]">
                    <span class="text-grey-900">Notifications Sent</span> 
                    <span class="text-grey-500">{valueX}</span>
                    </div>
                  </div>`,
        },
      },
    },
  },
};
