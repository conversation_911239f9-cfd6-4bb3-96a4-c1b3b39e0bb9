import { useLayoutEffect, useMemo, useRef } from "react";
import am5themes_Animated from "@amcharts/amcharts5/themes/Animated";
import * as am5 from "@amcharts/amcharts5";
import * as am5xy from "@amcharts/amcharts5/xy";
import { cn } from "../../common/utils";
import { getMergedConfigs } from "../utils/coreUtils";
import { getDefaultConfig } from "./StackedBarDefaultConfig";
import { type StackedBarProps } from "./types";
import { getDatavizTheme } from "../hooks";

const StackedBar = (props: StackedBarProps) => {
  const { data, dataKey, customConfig, chartId, onBarClick, theme } = props;

  const mergedConfig = useMemo(
    () =>
      getMergedConfigs(getDefaultConfig(theme), customConfig.stackedBarProps),
    [theme, customConfig.stackedBarProps],
  );

  const {
    containerClass,
    gridContainerProps,
    chartColors,
    tooltipProps,
    columnProps,
    xGridProps,
    yGridProps,
    yAxisProps,
    xAxisProps,
    xAxisLabelProps,
    xAxisGridProps,
    chartLabelProps,
    legendProps,
  } = mergedConfig;

  const { seriesData, chartData } = data;

  const stackedBarRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    if (!stackedBarRef.current) return;

    const {
      labelColor,
      tooltipLabelColor,
      tooltipStrokeColor,
      tooltipFillColor,
      tooltipShadowColor,
    } = chartColors ?? {};

    const {
      tooltipGraphicsConfig,
      tooltipLabelConfig,
      tooltipHTML,
      tooltipSettingsConfig,
    } = tooltipProps ?? {};

    const root = am5.Root.new(stackedBarRef.current);
    root.setThemes([
      am5themes_Animated.new(root),
      getDatavizTheme(root, theme),
    ]);

    // Create chart
    const chart = root.container.children.push(
      am5xy.XYChart.new(root, {
        panX: false,
        panY: false,
        paddingLeft: 0,
        wheelX: "none",
        wheelY: "none",
        paddingTop: 0,
        layout: root.verticalLayout,
      }),
    );

    if (gridContainerProps) {
      chart.gridContainer.setAll({ ...gridContainerProps });
    }

    const colors = seriesData
      .filter((s) => s.colorCode)
      ?.map((s) => am5.color(s.colorCode));
    chart?.get("colors")?.set("colors", colors);

    // Create axes
    const yRenderer = am5xy.AxisRendererY.new(root, {});
    yRenderer.grid.template.setAll({ location: 1, ...yGridProps });
    const yAxis = chart.yAxes.push(
      am5xy.CategoryAxis.new(root, {
        categoryField: dataKey,
        renderer: yRenderer,
        ...yAxisProps,
      }),
    );
    yAxis.data.setAll(chartData);

    const xRenderer = am5xy.AxisRendererX.new(root, {
      ...xAxisGridProps,
    });
    xRenderer.grid.template.setAll({ ...xGridProps });
    xRenderer.labels.template.setAll({
      ...(labelColor ? { fill: am5.color(labelColor) } : {}),
      ...xAxisLabelProps,
    });
    const xAxis = chart.xAxes.push(
      am5xy.ValueAxis.new(root, {
        renderer: xRenderer,
        min: 0,
        max: chartData[0]?.total,
        strictMinMax: true,
        ...xAxisProps,
      }),
    );

    // Add series
    function makeSeries(name: string, fieldName: string) {
      const series = chart.series.push(
        am5xy.ColumnSeries.new(root, {
          name: name,
          stacked: true,
          xAxis: xAxis,
          yAxis: yAxis,
          baseAxis: yAxis,
          valueXField: fieldName,
          categoryYField: dataKey,
          marginTop: 0,
          legendValueText: `{${dataKey}}`,
        }),
      );
      const tooltip = am5.Tooltip.new(root, {
        getFillFromSprite: false,
        getStrokeFromSprite: false,
        autoTextColor: false,
        getLabelFillFromSprite: false,
        centerY: 30,
        paddingTop: 2,
        paddingBottom: 2,
        ...tooltipSettingsConfig,
      });

      tooltip.get("background")?.setAll({
        ...tooltipGraphicsConfig,
        ...(tooltipFillColor ? { fill: am5.color(tooltipFillColor) } : {}),
        ...(tooltipStrokeColor
          ? { stroke: am5.color(tooltipStrokeColor) }
          : {}),
        ...(tooltipShadowColor
          ? { shadowColor: am5.color(tooltipShadowColor) }
          : {}),
      });

      tooltip.label.setAll({
        ...tooltipLabelConfig,
        ...(tooltipLabelColor ? { fill: am5.color(tooltipLabelColor) } : {}),
      });

      series.columns.template.setAll({
        tooltip,
        tooltipHTML: tooltipHTML,
        cursorOverStyle: "pointer",
        tooltipX: am5.percent(50),
        tooltipY: am5.percent(85),
        ...columnProps,
      });
      series.data.setAll(chartData);

      if (onBarClick) {
        series.columns.template.events.on("click", (e) => {
          const dataContext = e.target.dataItem?.dataContext;
          const dataSettings = e.target.dataItem?.component?._settings as {
            valueXField: string;
          };
          if (dataContext) {
            onBarClick(
              dataContext as StackedBarProps["data"]["chartData"],
              dataSettings?.valueXField,
            );
          }
        });
      }

      series.appear().catch((e) => {
        throw e;
      });
    }

    if (chartLabelProps) {
      const text = chartLabelProps.text ?? "";

      chart.children.unshift(
        am5.Label.new(root, {
          fontSize: 16,
          fontWeight: "500",
          paddingLeft: 12,
          textAlign: "center",
          lineHeight: 1.6,
          x: 0,
          centerX: 0,
          dx: -10,
          paddingTop: 0,
          ...chartLabelProps,
          text,
        }),
      );
    }

    seriesData?.forEach((d) => makeSeries(d.label, d.propertyName));

    const legend = chart.children.push(
      am5.Legend.new(root, {
        ...(legendProps?.direction === "up"
          ? { x: am5.percent(100), centerX: am5.percent(100) }
          : {}),
        clickTarget: "none",
        ...legendProps?.legendSetting,
      }),
    );

    legend.labels.template.setAll({
      ...legendProps?.legendLabelProps,
      text: legendProps?.legendLabelProps?.text ?? "",
    });
    legend.markers.template.setAll({
      ...legendProps?.legendMarkerProps,
    });
    if (legendProps?.legendRectangle) {
      legend.markerRectangles.template.setAll({
        cornerRadiusTL: 10,
        cornerRadiusTR: 10,
        cornerRadiusBL: 10,
        cornerRadiusBR: 10,
      });
    }

    //value labels disabled
    legend.valueLabels.template.set("forceHidden", true);

    const legendsContainer =
      legendProps?.direction === "up"
        ? chart.children.unshift(
            am5.Container.new(root, {
              width: am5.p100,
              layout: root.gridLayout,
              ...legendProps?.legendContainerSetting,
            }),
          )
        : chart.children.push(
            am5.Container.new(root, {
              width: am5.p100,
              layout: root.gridLayout,
              ...legendProps?.legendContainerSetting,
            }),
          );

    chart.series.each((series) => legend.data.push(series));

    legendsContainer.children.push(legend);
    void chart.appear(100, 100);

    return () => root.dispose();
  }, [
    seriesData,
    chartData,
    dataKey,
    gridContainerProps,
    xGridProps,
    yGridProps,
    columnProps,
    xAxisProps,
    yAxisProps,
    xAxisGridProps,
    xAxisLabelProps,
    chartColors,
    tooltipProps,
    chartLabelProps,
    legendProps,
    onBarClick,
  ]);

  return (
    <div
      id={chartId}
      className={cn("h-full", containerClass)}
      data-testid={chartId}
      ref={stackedBarRef}
    />
  );
};

export default StackedBar;
