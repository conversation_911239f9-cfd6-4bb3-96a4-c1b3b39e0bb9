import { colors } from "@zs-nimbus/foundations";
import * as am5 from "@amcharts/amcharts5";
import { defaultTheme } from "./constants";
import { ThemeType } from "./types";

const applyTooltipTheme = (chartTheme: am5.Theme, theme: ThemeType) => {
  chartTheme.rule("Tooltip").setAll({
    getFillFromSprite: false,
    getLabelFillFromSprite: false,
    getStrokeFromSprite: false,
    autoTextColor: false,
    paddingBottom: 0,
    paddingLeft: 0,
    paddingRight: 0,
    paddingTop: 0,
  });

  chartTheme.rule("PointedRectangle", ["tooltip", "background"]).setAll({
    stroke: am5.color(colors[theme].brand.white),
    fill: am5.color(colors[theme].brand.white),
    shadowColor: am5.color(colors[theme].content.base.primary),
    pointerLength: 10,
    pointerBaseWidth: 10,
    fillOpacity: 1,
    shadowBlur: 10,
    shadowOffsetX: 2,
    shadowOffsetY: 3,
    shadowOpacity: 0.4,
    strokeWidth: 0.00001,
    strokeOpacity: 0.1,
  });
};

// Grid Styling
const applyGridTheme = (chartTheme: am5.Theme, theme: ThemeType) => {
  chartTheme.rule("Grid", ["x"]).setAll({
    strokeWidth: 1,
    strokeOpacity: 0.5,
    stroke: am5.color("#E9E9E9"), //TODO no nimbus color
  });

  chartTheme.rule("Grid", ["y"]).setAll({
    strokeWidth: 0,
  });
};

// Text Styles (Axis Labels)
const applyAxisLabelTheme = (chartTheme: am5.Theme, theme: ThemeType) => {
  chartTheme.rule("Label", ["axis", "x"]).setAll({
    fontSize: 12,
    fontWeight: "400",
    fill: am5.color(colors[theme].content.base.tertiary),
    fontStyle: "normal",
    textAlign: "center",
    paddingTop: 12,
    paddingBottom: 12,
  });

  chartTheme.rule("Label", ["axis", "y"]).setAll({
    fontSize: 12,
    fontWeight: "400",
    fill: am5.color(colors[theme].content.base.tertiary),
    fontStyle: "normal",
    textAlign: "left",
    paddingRight: 12,
    oversizedBehavior: "truncate",
    ellipsis: "...",
  });
};

const applyThemeRules = (root: am5.Root, theme: ThemeType) => {
  const chartTheme = am5.Theme.new(root);
  applyTooltipTheme(chartTheme, theme);
  applyGridTheme(chartTheme, theme);
  applyAxisLabelTheme(chartTheme, theme);
  return chartTheme;
};

export const getDatavizTheme = (
  root: am5.Root,
  theme: ThemeType = defaultTheme,
) => applyThemeRules(root, theme);
