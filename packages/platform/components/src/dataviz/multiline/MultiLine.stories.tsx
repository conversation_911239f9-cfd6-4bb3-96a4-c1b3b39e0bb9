import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Card } from "@zs-nimbus/core";
import { getTooltipLabelHtml } from "../utils/tooltips";
import { getHoursAgo, getLineGradData } from "../utils";
import MultiLine<PERSON>hart from "./MultiLine";
import { multiLineData } from "./MultiLine.Data";

const meta = {
  title: "dataviz",
  component: MultiLineChart,
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/wLxNEe788V2qdOKQDy31Of/CXO---Desktop-(Analytics)?type=design&node-id=2434-55252&mode=design&t=deBeYFA7WP3acawi-0",
    },
  },
} as Meta<typeof MultiLineChart>;

export default meta;
type Story = StoryObj<typeof meta>;

export const MultiLineChartDefault: Story = {
  args: {
    theme: "light",
    data: multiLineData,
    axisKeys: ["date", "value"],
    config: {
      containerClass: "min-h-[500px]",
      chartProps: {
        chartSettings: {
          maxTooltipDistance: -1,
        },
      },
      rangeAxisProps: {
        xAxisSetting: {
          max: getHoursAgo(24 * 0.4, "ago"),
          min: getHoursAgo(24 * 9, "ago"),
          strictMinMax: true,
        },
        xRangeProps: [
          {
            rangeColor: "rgba(12,213,11,0.5)",
            fillSetting: { fillOpacity: 0.2 },
            data: {
              value: getHoursAgo(2.1 * 24, "ago"),
              endValue: getHoursAgo(3.5 * 24, "ago"),
            },
          },
        ],
      },
      axisProps: {
        xAxisProps: {
          visible: true,
          cellStartLocation: 1,
          minGridDistance: 60,
        },
        xGridProps: {},
        yAxisProps: {
          visible: true,
        },
        yGridProps: {
          visible: true,
        },
        xLabelProps: {
          labelColor: "#767676",
          paddingTop: 12,
          paddingBottom: 12,
          fontWeight: "400",
          fontStyle: "normal",
          fontSize: "12px",
          rotation: 45,
        },
        yLabelProps: {
          labelColor: "#767676",
          paddingRight: 12,
          fontWeight: "400",
          fontStyle: "normal",
          fontSize: "12px",
        },
        yAxisTitleSetting: {
          text: "Transactions (K)",
          rotation: 270,
          labelColor: "#767676",
          fontWeight: "400",
          fontStyle: "normal",
          fontSize: "12px",
        },
      },
      smoothLineSetting: {
        tension: 0,
      },
      legendsProps: {
        showLegend: true,
        direction: "bottom",
        showCustomIcon: false,
        legendMarkerProps: {
          width: 14,
          height: 14,
        },
        legendLabelProps: {
          marginRight: 14,
          fontSize: 14,
          fontWeight: "400",
          legendLabelColor: "rgba(25, 76, 187, 1)",
        },
        xLegendPosition: 68,
        yLegendPosition: 0,
        legendRectangle: {
          cornerRadiusTL: 2,
          cornerRadiusTR: 2,
          cornerRadiusBR: 2,
          cornerRadiusBL: 2,
        },
      },
      cursorProps: {
        xCursorSetting: {
          strokeColor: "rgba(186, 194, 207, 1)",
        },
        yCursorSetting: {
          opacity: 0,
          visible: false,
        },
      },
      bulletProps: {},
      dualYAxisProps: { showDualAxis: true },
    },
  },
};

export const MultiLineCharts: Story = {
  decorators: [(Story) => <Story />],
};

MultiLineCharts.decorators = [
  () => (
    <div className="mx-l">
      <Card className="mt-rem-160">
        <Card.Header>Trend Line Chart</Card.Header>
        <div>
          <MultiLineChart
            {...{
              theme: "light",
              chartType: "trendLine",
              data: [
                {
                  meta: {
                    lineColor: "#194CBB",
                    name: "Phishing",
                    strokeWidth: 3,
                    areaProps: {
                      gradientProps: {
                        showGradient: true,
                        stops: [
                          {
                            color: "rgba(33, 96, 225, 1)",
                            opacity: 0.5,
                          },
                          {
                            color: "rgba(33, 96, 225, 0.5)",
                            opacity: 0.2,
                          },
                        ],
                        rotation: 90,
                      },
                    },
                  },
                  data: [
                    {
                      date: "Jan 1",
                      value: 1.8,
                    },
                    {
                      date: "Jan 7",
                      value: 2,
                    },
                    {
                      date: "Jan 14",
                      value: 2.5,
                    },
                    {
                      date: "Jan 21",
                      value: 1.5,
                    },
                    {
                      date: "Jan 28",
                      value: 2.3,
                    },
                  ],
                },
              ],
              axisKeys: ["date", "value"],
              config: {
                containerClass: "h-[300px] w-full",
                axisProps: {
                  xGridProps: {
                    strokeWidth: 2,
                    visible: false,
                  },
                  yAxisProps: {
                    visible: false,
                  },
                  yGridProps: { visible: false },
                  yLabelProps: {
                    visible: false,
                  },
                  yAxisSetting: {
                    min: 0,
                  },
                },
              },
            }}
          />
        </div>
      </Card>
      <Card>
        <Card.Header>
          Line chart with legends, tooltip, ranges, custom labels, axis title
        </Card.Header>
        <div>
          <MultiLineChart
            {...{
              theme: "light",
              data: multiLineData.map((d) =>
                d.meta.areaProps
                  ? {
                      ...d,
                      meta: {
                        ...d.meta,
                        areaProps: {
                          ...d.meta.areaProps,
                          gradientProps: {
                            ...d.meta.areaProps.gradientProps,
                            showGradient: true,
                          },
                        },
                      },
                    }
                  : d,
              ),
              axisKeys: ["date", "value"],
              config: {
                containerClass: "min-h-[500px]",
                chartProps: {
                  chartSettings: {
                    maxTooltipDistance: -1,
                  },
                },
                rangeAxisProps: {
                  xAxisSetting: {
                    max: getHoursAgo(24 * 0.4, "ago"),
                    min: getHoursAgo(24 * 9, "ago"),
                    strictMinMax: true,
                  },
                  xRangeProps: [
                    {
                      rangeColor: "rgba(12,213,11,0.5)",
                      fillSetting: { fillOpacity: 0.2 },
                      data: {
                        value: getHoursAgo(2.1 * 24, "ago"),
                        endValue: getHoursAgo(3.5 * 24, "ago"),
                      },
                    },
                  ],
                },
                axisProps: {
                  xAxisSetting: {
                    tooltipSetting: {
                      labelHTML: `
                        <div>date : {date}</div>
                        `,
                    },
                  },
                  xAxisProps: {
                    cellStartLocation: 1,
                    minGridDistance: 60,
                  },
                  xLabelProps: {
                    rotation: 45,
                  },
                  yAxisTitleSetting: {
                    text: "Transactions (K)",
                  },
                },
                smoothLineSetting: {
                  tension: 0,
                },
                legendsProps: {
                  showLegend: true,
                  direction: "bottom",
                  showCustomIcon: false,
                  legendMarkerProps: {
                    width: 14,
                    height: 14,
                  },
                  legendLabelProps: {
                    marginRight: 14,
                    fontSize: 14,
                    fontWeight: "400",
                    legendLabelColor: "rgba(25, 76, 187, 1)",
                  },
                  xLegendPosition: 68,
                  yLegendPosition: 0,
                  legendRectangle: {
                    cornerRadiusTL: 2,
                    cornerRadiusTR: 2,
                    cornerRadiusBR: 2,
                    cornerRadiusBL: 2,
                  },
                },
                tooltipProps: {
                  showCombinedTooltip: true,
                  noValueText: "NA",
                  tooltipSetting: {
                    labelText: "",
                  },
                  getTooltipLabelHtml,
                },
                dualYAxisProps: { showDualAxis: true },
              },
            }}
          />
        </div>
      </Card>
      <Card className="mt-rem-160">
        <Card.Header>Line chart with Dual axis</Card.Header>
        <Card.Body>
          <MultiLineChart
            {...{
              theme: "light",
              data: multiLineData,
              axisKeys: ["date", "value"],
              config: {
                containerClass: "min-h-[550px]",
                axisProps: {
                  xAxisProps: {
                    minorGridEnabled: true,
                  },
                },
                legendsProps: {
                  direction: "right",
                  showLegend: true,
                  showCustomIcon: true,
                  legendMarkerProps: {
                    width: 14,
                    height: 14,
                  },
                  legendLabelProps: {
                    marginRight: 14,
                    fontSize: 14,
                    fontWeight: "400",
                    legendLabelColor: "rgba(25, 76, 187, 1)",
                  },
                  xLegendPosition: 68,
                },
                tooltipProps: {
                  tooltipSetting: {
                    labelHTML: `<div>
                  <div>value : {value}</div>
                  </div>`,
                  },
                },
                dualYAxisProps: {
                  showDualAxis: true,
                },
              },
            }}
          />
        </Card.Body>
      </Card>

      <Card>
        <Card.Header>Strict Min max</Card.Header>

        <Card.Body>
          <MultiLineChart
            {...{
              theme: "light",
              data: [
                {
                  meta: {
                    lineColor: "#194CBB",
                    name: "Phishing",
                    strokeWidth: 3,
                  },
                  data: [
                    {
                      date: "Jan 1",
                      value: 1.8,
                    },
                    {
                      date: "Jan 7",
                      value: 2,
                    },
                    {
                      date: "Jan 14",
                      value: 2.5,
                    },
                    {
                      date: "Jan 21",
                      value: 1.5,
                    },
                    {
                      date: "Jan 28",
                      value: 2.3,
                    },
                  ],
                },
              ],
              axisKeys: ["date", "value"],
              config: {
                containerClass: "min-h-[550px]",
                axisProps: {
                  xAxisProps: {
                    minorGridEnabled: true,
                  },
                  yAxisSetting: {
                    min: 0,
                    max: 3,
                    strictMinMax: true,
                    numberFormat: "#.#k",
                  },
                  yLabelProps: {
                    showCustomLabel: true,
                    labelFormatter: (text = "") => {
                      if (text?.toLowerCase() === "0k") {
                        return "0";
                      }

                      return text;
                    },
                  },
                },
                legendsProps: {
                  showCustomIcon: false,
                  legendMarkerProps: {
                    width: 14,
                    height: 14,
                  },
                  legendLabelProps: {
                    marginRight: 14,
                    fontSize: 14,
                    fontWeight: "400",
                    legendLabelColor: "rgba(25, 76, 187, 1)",
                  },
                  xLegendPosition: 68,
                  yLegendPosition: 0,
                },
                tooltipProps: {
                  tooltipSetting: {
                    labelHTML: `<div>
          <div>value : {value}</div>
          </div>`,
                  },
                },
                cursorProps: {},
                dualYAxisProps: {
                  labels: [
                    {
                      text: "legends.good",
                      y: 15,
                      fill: "",
                    },
                    {
                      text: "legends.okay",
                      y: 48,
                      fill: "",
                    },
                    {
                      text: "legends.poor",
                      y: 82,
                      fill: "",
                    },
                  ],
                  gradientProps: {
                    stops: getLineGradData(3, "light"),
                    rotation: 0,
                  },
                },
              },
            }}
          />
        </Card.Body>
      </Card>
    </div>
  ),
];
