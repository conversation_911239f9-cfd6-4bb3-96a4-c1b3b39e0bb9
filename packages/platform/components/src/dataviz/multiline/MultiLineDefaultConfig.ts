import { colors } from "@zs-nimbus/foundations";
import { getTooltipLabelHtml } from "../utils/tooltips";
import { getLineGradData } from "../utils";
import { type ThemeType } from "../common/types";
import { MultilineType, type MultiLineChartDefaultConfigProps } from "./types";
import { datavizColors } from "@zs-nimbus/dataviz-colors";

export const MultiLineDefaultConfig = (
  theme: ThemeType,
  chartType?: MultilineType,
): MultiLineChartDefaultConfigProps => {
  return {
    containerClass: "min-h-[550px]",
    axisProps: {
      xAxisProps: {
        visible: true,
      },
      yAxisProps: {
        visible: true,
      },
      xGridProps: {
        strokeWidth: 1,
        visible: true,
      },
      yGridProps: {
        visible: true,
        strokeWidth: 1,
      },
      yLabelProps: {
        visible: true,
        paddingRight: 12,
      },
      yAxisTitleSetting: {
        rotation: 270,
        labelColor: colors[theme].content.base.tertiary,
        fontWeight: "400",
        fontStyle: "normal",
        fontSize: "12px",
        marginLeft: -10,
      },
    },
    chartProps: {
      topAxesContainer: {
        visible: false,
      },
      chartSettings: {
        paddingLeft: 0,
      },
      ...(chartType === "trendLine"
        ? {
            chartSettings: {
              marginLeft: 0,
              paddingRight: 0,
              paddingLeft: 0,
              marginRight: 0,
              marginTop: 0,
              paddingTop: 0,
              paddingBottom: 0,
              marginBottom: 0,
            },
            bottomAxesContainer: {
              visible: false,
            },
            topAxesContainer: {
              visible: false,
            },
            leftContainer: {
              visible: false,
            },
          }
        : {}),
    },

    tooltipProps: {
      showTooltip: true,
      tooltipBackgroundSetting: {},
      tooltipSetting: {
        labelHTML: `<div>
            <div>value : {value}</div>
            <div>date : {date.formatDate()}</div>
            </div>`,
      },
      getTooltipLabelHtml,
    },

    legendsProps: {
      legendRectangle: {
        cornerRadiusTL: 10,
        cornerRadiusTR: 10,
        cornerRadiusBL: 10,
        cornerRadiusBR: 10,
      },
      legendMarkerProps: {
        width: 8,
        height: 8,
      },
      xLegendPosition: 0,
      yLegendPosition: 0,
    },
    cursorProps: {
      showCursor: true,
      xCursorSetting: {
        strokeColor: colors[theme].border.base.tertiary,
        strokeWidth: 2,
        strokeDasharray: [5, 5],
      },
      yCursorSetting: {
        opacity: 0,
        visible: false,
        strokeWidth: 2,
        strokeDasharray: [5, 5],
      },
    },
    dualYAxisProps: {
      gradientProps: {
        stops: getLineGradData(3, "light"),
        rotation: 0,
      },
    },
    bulletProps: {
      showBullets: true,
      strokeColor: datavizColors[theme].dataviz.neutrals.gaps,
    },
    ...(chartType === "trendLine"
      ? {
          cursorProps: {
            showCursor: false,
          },
          bulletProps: { showBullets: false },
        }
      : {}),
  };
};
