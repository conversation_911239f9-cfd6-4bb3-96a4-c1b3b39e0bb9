import type * as am5 from "@amcharts/amcharts5";
import type * as am5xy from "@amcharts/amcharts5/xy";
import {
  DatavizColorType,
  type AxisProps,
  type ChartProps,
  type CursorProps,
  type DualYAxisProps,
  type GradientProps,
  type LegendsProps,
  type LineProps,
  type ThemeType,
  type TooltipBackgroundSetting,
} from "../common/types";

export type XYAxisProps = AxisProps;

export type MultilineAreaProps = am5.IGraphicsSettings & {
  gradientProps: GradientProps;
};

export type MultiLineProps = LineProps & {
  name: string;
  legendIcon?: string;
  unit?: string;
  areaProps?: MultilineAreaProps;
  tooltipProps?: MultiLineTooltipProps;
};

export type TooltipData = {
  title?: string;
  label?: string;
  lineColor?: string;
  value?: string | number;
};

export type MultiLineTooltipProps = {
  tooltipSetting?: am5.ITooltipSettings;
  showTooltip?: boolean;
  tooltipHTML?: string;
  showCombinedTooltip?: boolean;
  setCombinedTooltipData?: (a: Record<string, number | string>) => TooltipData;
  tooltipBackgroundSetting?: TooltipBackgroundSetting;
  getTooltipLabelHtml?: (a: TooltipData[]) => string;
  valueFormatter?: (value: number) => string;
  metaRow?: TooltipData;
  noValueText?: string;
};

export type RangeAxisProps = Omit<AxisProps, "xAxisSetting"> & {
  xAxisSetting: Partial<am5xy.IValueAxisSettings<am5xy.AxisRenderer>> & {
    tooltipSetting?: am5.ITooltipSettings;
    tooltipBackgroundSetting?: am5.IRectangleSettings & {
      fillColor?: string;
    };
  };
};

export type MultiLineChartDefaultConfigProps = {
  containerClass?: string;
  chartProps?: ChartProps;
  axisProps?: AxisProps;
  rangeAxisProps?: RangeAxisProps;
  legendsProps?: LegendsProps;
  dualYAxisProps?: DualYAxisProps;
  tooltipProps?: MultiLineTooltipProps;
  smoothLineSetting?: am5xy.ISmoothedXYLineSeriesDataItem &
    Partial<am5xy.SmoothedXYLineSeriesProperties>;
  numberFormatter?: am5.INumberFormatterSettings;
  cursorProps?: CursorProps;
  bulletProps?: am5.ILabelSettings & {
    showBullets?: boolean;
    bulletColor?: string;
  } & DatavizColorType;
};

export type LineData = Record<
  string,
  string | number | boolean | undefined | null
>;

export type MultiLineData = {
  meta: MultiLineProps;
  data: LineData[];
};

export type MultilineType = "line" | "trendLine";

export type MultiLineChartProps = {
  data: MultiLineData[];
  config?: MultiLineChartDefaultConfigProps;
  axisKeys: [string, string];
  chartId?: string;
  theme: ThemeType;
  chartType?: MultilineType;
};
