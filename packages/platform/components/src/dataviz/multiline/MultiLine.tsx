import { useLayoutEffect, useMemo, useRef } from "react";
import * as am5 from "@amcharts/amcharts5";
import * as am5xy from "@amcharts/amcharts5/xy";
import am5themes_Animated from "@amcharts/amcharts5/themes/Animated";
import { cn } from "../../common/utils";
import { getMergedConfigs } from "../utils";
import {
  getChartLegendsAtBottom,
  getChartLegendsAtRight,
} from "../utils/legendUtils";
import { createRange, createRangeAxis } from "../utils/rangeUtils";
import { useDualAxis } from "../hooks";
import { MultiLineDefaultConfig as defaultConfig } from "./MultiLineDefaultConfig";
import { getDatavizTheme } from "../common/datavizTheme";
import {
  type TooltipData,
  type MultiLineChartProps,
  type MultiLineProps,
} from "./types";

const MultiLine = (props: MultiLineChartProps) => {
  const {
    config,
    axisKeys,
    data: linesData,
    chartId,
    theme,
    chartType,
  } = props;

  const multiLineChartRef = useRef<HTMLDivElement>(null);

  const mergedConfig = useMemo(
    () => getMergedConfigs(defaultConfig(theme, chartType), config),
    [config, chartType],
  );

  const {
    containerClass,
    axisProps,
    rangeAxisProps,
    legendsProps,
    cursorProps,
    smoothLineSetting,
    dualYAxisProps,
    bulletProps,
    chartProps,
    numberFormatter,
  } = { ...mergedConfig };

  const [xAxisDataKey, yAxisDataKey] = axisKeys;
  const { renderDualAxis } = useDualAxis({ dualYAxisProps });

  useLayoutEffect(() => {
    if (!multiLineChartRef.current) return;
    const root = am5.Root.new(multiLineChartRef.current);

    root.setThemes([
      am5themes_Animated.new(root),
      getDatavizTheme(root, theme),
    ]);

    root.numberFormatter.setAll({
      ...numberFormatter,
    });

    const chart = root.container.children.push(
      am5xy.XYChart.new(root, {
        layout: root.verticalLayout,
        ...chartProps?.chartSettings,
      }),
    );

    chart.leftAxesContainer.setAll({
      ...chartProps?.leftContainer,
    });

    chart.rightAxesContainer.setAll({
      ...chartProps?.rightContainer,
    });

    chart.chartContainer.setAll({
      ...chartProps?.chartContainer,
    });

    chart.bottomAxesContainer.setAll({
      ...chartProps?.bottomAxesContainer,
    });

    chart.topAxesContainer.setAll({
      ...chartProps?.topAxesContainer,
    });

    chart.gridContainer.setAll({
      ...chartProps?.gridContainer,
    });

    const {
      xAxisSetting,
      yAxisSetting,
      xAxisProps,
      yAxisProps,
      xGridProps,
      yGridProps,
      xLabelProps,
      yLabelProps,
      yAxisTitleSetting,
      xAxisTitleSetting,
    } = axisProps ?? {};

    const xRenderer = am5xy.AxisRendererX.new(root, {
      strokeOpacity: 0.1,
      minGridDistance: 60,
      ...xAxisProps,
    });

    xRenderer.grid.template.setAll({
      ...xGridProps,
    });

    xRenderer.labels.template.setAll({
      ...(xLabelProps?.labelColor
        ? { fill: am5.color(xLabelProps?.labelColor) }
        : {}),
      ...xLabelProps,
      oversizedBehavior: "wrap",
      textAlign: "center",
    });

    /**
     * below is the way to add custom x axis ticks(label)
     * **/
    if (xLabelProps?.showCustomLabel) {
      xRenderer.labels.template.adapters.add("text", function (text, target) {
        if (target.dataItem?.dataContext) {
          const context = target.dataItem.dataContext as {
            xLabel: string;
            [xAxisDataKey: string]: string;
          };
          const { xLabel } = context || {};

          return (
            xLabelProps?.labelFormatter?.(context?.[xAxisDataKey]) ?? xLabel
          );
        }

        return text;
      });
    }

    const xAxisTooltip = am5.Tooltip.new(root, {
      ...(xAxisSetting?.tooltipBackgroundSetting
        ? {
            background: am5.Rectangle.new(root, {
              ...(xAxisSetting?.tooltipBackgroundSetting?.fillColor
                ? {
                    fill: am5.color(
                      xAxisSetting.tooltipBackgroundSetting?.fillColor,
                    ),
                  }
                : {}),
              ...xAxisSetting?.tooltipBackgroundSetting,
            }),
          }
        : {}),
      ...xAxisSetting?.tooltipSetting,
    });

    const xAxis = chart.xAxes.push(
      am5xy.CategoryAxis.new(root, {
        categoryField: xAxisDataKey,
        renderer: xRenderer,
        tooltip: xAxisTooltip,
        startLocation: 0.5,
        endLocation: 0.5,
        ...xAxisSetting,
      }),
    );

    xAxis.set(
      "tooltip",
      am5.Tooltip.new(root, {
        forceHidden: true,
      }),
    );
    xAxis.onPrivate("cellWidth", function (cellWidth) {
      xRenderer.labels.template.set("maxWidth", cellWidth);
    });

    const yRenderer = am5xy.AxisRendererY.new(root, {
      ...yAxisProps,
    });

    yRenderer.grid.template.setAll({ ...yGridProps });

    yRenderer.labels.template.setAll({
      ...(yLabelProps?.labelColor
        ? { fill: am5.color(yLabelProps?.labelColor) }
        : {}),
      ...yLabelProps,
    });

    if (yLabelProps?.showCustomLabel) {
      yRenderer.labels.template.adapters.add("text", function (text, target) {
        if (yLabelProps.labelFormatter) {
          return yLabelProps?.labelFormatter?.(text);
        }
        if (target.dataItem?.dataContext) {
          const { yLabel } = target.dataItem.dataContext as { yLabel: string };

          return yLabel;
        }

        return text;
      });
    }

    const yAxisTooltip = am5.Tooltip.new(root, {
      ...(yAxisSetting?.tooltipBackgroundSetting
        ? {
            background: am5.Rectangle.new(root, {
              ...(yAxisSetting?.tooltipBackgroundSetting?.fillColor
                ? {
                    fill: am5.color(
                      yAxisSetting.tooltipBackgroundSetting?.fillColor,
                    ),
                  }
                : {}),
              ...yAxisSetting?.tooltipBackgroundSetting,
            }),
          }
        : {}),
      ...yAxisSetting?.tooltipSetting,
    });

    const yAxis = chart.yAxes.push(
      am5xy.ValueAxis.new(root, {
        maxDeviation: 0,
        renderer: yRenderer,
        tooltip: yAxisTooltip,
        ...yAxisSetting,
      }),
    );

    yAxis.set(
      "tooltip",
      am5.Tooltip.new(root, {
        forceHidden: true,
      }),
    );

    if (yAxisTitleSetting?.text) {
      const yAxisTitle = am5.Label.new(root, {
        y: am5.p50,
        centerX: am5.p50,
        centerY: am5.p0,
        ...(yAxisTitleSetting?.labelColor
          ? { fill: am5.color(yAxisTitleSetting?.labelColor) }
          : {}),
        text: yAxisTitleSetting.text,
        ...yAxisTitleSetting,
      });
      yAxis.children.unshift(yAxisTitle);
    }

    if (xAxisTitleSetting?.text) {
      const xAxisTitle = am5.Label.new(root, {
        y: am5.p50,
        centerX: am5.p50,
        x: am5.p0,
        centerY: am5.p0,
        ...(xAxisTitleSetting?.labelColor
          ? { fill: am5.color(xAxisTitleSetting?.labelColor) }
          : {}),
        ...xAxisTitleSetting,
      });
      xAxis.children.unshift(xAxisTitle);
    }

    linesData?.forEach(({ data, meta }, i) => {
      const {
        lineColor,
        gradientProps: lineGradientProps,
        name,
        areaProps,
        tooltipProps: { showTooltip: showLineTooltip = true } = {},
        ...rest
      } = meta || {};

      const tooltipProps = {
        ...defaultConfig(theme).tooltipProps,
        ...config?.tooltipProps,
      };

      const {
        tooltipSetting,
        tooltipBackgroundSetting,
        getTooltipLabelHtml,
        valueFormatter,
      } = tooltipProps;

      const tooltip = tooltipProps?.showTooltip
        ? {
            tooltip: am5.Tooltip.new(root, {
              labelHTML: tooltipSetting?.labelHTML,
              pointerOrientation: "horizontal",
              autoTextColor: false,
              /**
               * userData : A storage for any custom user data
               * that needs to be associated with the element.
               * **/
              userData: meta,
              background: am5.PointedRectangle.new(root, {
                ...(tooltipBackgroundSetting?.shadowFillColor
                  ? {
                      shadowColor: am5.color(
                        tooltipBackgroundSetting?.shadowFillColor,
                      ),
                    }
                  : {}),
                ...(tooltipBackgroundSetting?.strokeColor
                  ? {
                      stroke: am5.color(tooltipBackgroundSetting?.strokeColor),
                    }
                  : {}),
                ...(tooltipBackgroundSetting?.fillColor
                  ? {
                      fill: am5.color(tooltipBackgroundSetting?.fillColor),
                    }
                  : {}),
                ...tooltipBackgroundSetting,
              }),
              ...tooltipSetting,
            }),
          }
        : {};

      tooltip.tooltip?.label.set("interactive", true);

      if (!showLineTooltip) {
        tooltip.tooltip?.set("forceHidden", true);
      }

      const series = chart.series.push(
        am5xy.SmoothedXLineSeries.new(root, {
          name: name ?? "Series" + i,
          userData: meta,
          xAxis: xAxis,
          yAxis: yAxis,
          connect: false,
          valueYField: yAxisDataKey,
          categoryXField: xAxisDataKey,
          legendValueText: `{${yAxisDataKey}}`,
          maskBullets: false,
          /**
           * add line color here to give color to line and legends
           * */
          ...(lineColor
            ? { stroke: am5.color(lineColor), fill: am5.color(lineColor) }
            : {}),
          ...tooltip,
          ...smoothLineSetting,
        }),
      );

      // TO add area to line chart
      const { gradientProps: areaGradientProps } = areaProps ?? {};
      if (areaGradientProps?.showGradient) {
        series.fills.template.setAll({
          visible: true,
          fillOpacity: 0.2,
          ...(areaGradientProps?.showGradient
            ? {
                fillGradient: am5.LinearGradient.new(root, {
                  ...areaGradientProps,
                  stops: areaGradientProps?.stops.map((stop) => ({
                    ...stop,
                    color: am5.color(stop.color),
                  })),
                }),
              }
            : {}),
          ...areaProps,
        });
      }

      if (tooltipProps?.showTooltip) {
        series.get("tooltip")?.adapters.add("labelHTML", function (_, target) {
          if (target.dataItem) {
            const context = target.dataItem?.dataContext as Record<
              string,
              number | string
            >;

            let tooltipData: TooltipData[] = [];
            if (tooltipProps.showCombinedTooltip) {
              chart.series.each(function (item) {
                const dataItems: Array<am5.DataItem<am5xy.IXYSeriesDataItem>> =
                  [...item.dataItems];

                const dataContext = dataItems.find((it) => {
                  const d = it?.dataContext as { date: string };

                  return d?.date === context?.date;
                })?.dataContext as { value: string };

                const { value = "" } = dataContext ?? {};

                const userData = item.get("userData") as MultiLineProps;
                let val = value;
                if (val === null || val === undefined) {
                  val = tooltipProps.noValueText ?? "NA";
                } else {
                  val = `${valueFormatter ? valueFormatter(Number(value)) : value} ${meta.unit ?? ""}`;
                }
                tooltipData.push({
                  title: userData.name,
                  label: context?.[xAxisDataKey] as string,
                  value: val,
                  lineColor: userData.lineColor,
                  ...(tooltipProps?.setCombinedTooltipData?.(context) ?? {}),
                });
              });
            } else {
              const userData = series.get("userData");
              const { value = "" } = context ?? {};
              let val = value;
              if (val === null || val === undefined) {
                val = tooltipProps.noValueText ?? "NA";
              } else {
                val = `${valueFormatter ? valueFormatter(Number(value)) : value} ${meta.unit ?? ""}`;
              }

              tooltipData = [
                {
                  title: userData.name,
                  value: val,
                  label: context?.[xAxisDataKey] as string,
                  lineColor: userData.lineColor,
                },
              ];
              if (tooltipProps?.metaRow) {
                tooltipData.push({ ...tooltipProps?.metaRow });
              }
            }

            return getTooltipLabelHtml?.(tooltipData);
          }
        });
      }

      const lineStroke = meta?.gradientProps?.showGradient
        ? {
            strokeGradient: am5.LinearGradient.new(root, {
              ...lineGradientProps,
              stops: lineGradientProps?.stops.map((stop) => ({
                ...stop,
                color: am5.color(stop.color),
              })),
            }),
          }
        : { ...(lineColor ? { stroke: am5.color(lineColor) } : {}) };

      series.strokes.template.setAll({
        ...lineStroke,
        ...rest,
      });

      series.data.setAll(data);
      void series.appear();
      xAxis.data.setAll(data);

      if (bulletProps) {
        series.bullets.push(function () {
          const circle = am5.Circle.new(root, {
            radius: 6,
            stroke: am5.color(0xffffff),
            strokeWidth: 3,
            fill: series.get("fill"),
            opacity: 0,
            focusable: true,
            ...(bulletProps?.bulletColor
              ? { fill: am5.color(bulletProps?.bulletColor) }
              : {}),
            ...bulletProps,
          });

          circle.states.create("default", {
            opacity: 0,
          });

          circle.states.create("hover", {
            opacity: 1,
          });

          return am5.Bullet.new(root, {
            sprite: circle,
          });
        });
      }

      // Constrain tooltip to plot container bounds
      series?.get("tooltip")?.adapters.add("bounds", function () {
        return chart.chartContainer.globalBounds();
      });
    });

    if (legendsProps?.showLegend) {
      const legendsData = linesData?.map(({ meta }) => ({
        name: meta.name,
        legendIcon: meta.legendIcon,
      }));

      if (legendsProps?.direction === "right") {
        getChartLegendsAtRight({
          chart,
          root,
          legendsProps: { ...legendsProps, legendsData },
        });
      } else {
        getChartLegendsAtBottom({
          root,
          chart,
          legendsProps: { ...legendsProps, legendsData },
        });
      }
    }

    if (axisProps?.yRangeProps) {
      axisProps?.yRangeProps.forEach((item) => createRange(yAxis, item, chart));
    }
    if (axisProps?.xRangeProps) {
      axisProps?.xRangeProps.forEach((item) => createRange(xAxis, item, chart));
    }

    if (cursorProps?.showCursor) {
      const cursor = am5xy.XYCursor.new(root, {
        xAxis: xAxis,
        ...cursorProps,
      });
      if (cursorProps.xCursorSetting) {
        cursor.lineX.setAll({
          ...(cursorProps.xCursorSetting.strokeColor
            ? {
                stroke: am5.color(cursorProps.xCursorSetting.strokeColor),
              }
            : {}),
        
          ...cursorProps.xCursorSetting,
        });
      }
      if (cursorProps.yCursorSetting) {
        cursor.lineY.setAll({
          ...(cursorProps.yCursorSetting.strokeColor
            ? {
                stroke: am5.color(cursorProps.yCursorSetting.strokeColor),
              }
            : {}),
         
          ...cursorProps.yCursorSetting,
        });
      }
      chart.set("cursor", cursor);
      if (bulletProps?.showBullets) {
        let previousBulletSprites: am5.Sprite[] = [];
        const cursorMoved = () => {
          previousBulletSprites.forEach((sprite) => sprite.unhover());
          previousBulletSprites = [];
          chart.series.each(function (ser) {
            const dataItem = ser.get("tooltip")?.dataItem;
            if (dataItem) {
              const bulletSprite = dataItem?.bullets?.[0]?.get?.("sprite");
              if (bulletSprite) {
                bulletSprite.hover();
                previousBulletSprites.push(bulletSprite);
              }
            }
          });
        };
        chart.get("cursor")?.events?.on("cursormoved", cursorMoved);
      }
    }

    if (dualYAxisProps?.showDualAxis) {
      renderDualAxis({ root, chart });
    }

    if (rangeAxisProps) {
      createRangeAxis({
        root,
        chart,
        rangeAxisProps,
      });
    }

    return () => root.dispose();
  }, [
    linesData,
    axisProps,
    xAxisDataKey,
    yAxisDataKey,
    cursorProps,
    legendsProps,
    smoothLineSetting,
    dualYAxisProps,
    renderDualAxis,
    chartProps,
    bulletProps,
    config?.tooltipProps,
    numberFormatter,
    rangeAxisProps,
  ]);

  return (
    <div
      data-testid={chartId}
      className={cn("h-full", containerClass)}
      ref={multiLineChartRef}
    />
  );
};

export default MultiLine;
