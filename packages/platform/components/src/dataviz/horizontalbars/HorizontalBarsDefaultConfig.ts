import { datavizColors } from "@zs-nimbus/dataviz-colors";
import { colors } from "@zs-nimbus/foundations";
import { getDefaultTooltip } from "../utils/tooltips";
import { type ThemeType } from "../common/types";
import { getLineGradData } from "../utils/coreUtils";
import { type HorizontalBarDefaultConfigProps } from "./types";

export const getDefaultConfig = (
  theme: ThemeType,
): HorizontalBarDefaultConfigProps => {
  return {
    containerClass: "flex h-[550px]",
    roundedColumnProps: {
      cornerRadiusTR: 4,
      cornerRadiusBR: 4,
      minWidth: 4,
    },
    columnProps: {
      strokeOpacity: 0,
    },
    axisProps: {
      xAxisProps: { visible: false },
      xAxisSetting: {
        autoZoom: false,
        min: 0,
        strictMinMaxSelection: true,
      },
      yAxisProps: {
        visible: true,
      },
      xGridProps: {
        strokeWidth: 1,
        visible: true,
      },
      yGridProps: { visible: false },
      yLabelProps: {
        centerX: 100,
        maxWidth: 100,
        width: 90,
      },
    },
    bulletProps: {
      fontSize: 12,
      bulletsLabelFormatter: function (val: string) {
        return val?.toString();
      },
    },
    chartProps: {
      topAxesContainer: {
        visible: false,
      },
    },
    tooltipProps: {
      showTooltip: true,
      getTooltipHtml: getDefaultTooltip,
    },
    dualYAxisProps: {
      labels: [
        {
          text: "legends.good",
          y: 16,
          fill: "",
        },
        {
          text: "legends.okay",
          y: 50,
          fill: "",
        },
        {
          text: "legends.poor",
          y: 83,
          fill: "",
        },
      ],
      gradientProps: {
        stops: getLineGradData(3, theme),
        rotation: 0,
      },
    },
    gradientProps: {
      showGradient: true,
      stops: [
        {
          color: datavizColors[theme].dataviz.sequential.blue.scale03,
          opacity: 1,
        },
        {
          color: datavizColors[theme].dataviz.sequential.blue.scale02,
          opacity: 0.8,
        },
      ],
      rotation: 0,
    },
  };
};
