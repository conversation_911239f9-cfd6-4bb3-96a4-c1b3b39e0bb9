import { useLayoutEffect, useRef, useMemo } from "react";
import am5themes_Animated from "@amcharts/amcharts5/themes/Animated";
import * as am5 from "@amcharts/amcharts5";
import * as am5xy from "@amcharts/amcharts5/xy";
import { getColorByKey, getMergedConfigs } from "../utils/coreUtils";
import { cn } from "../../common/utils";
import { getChartLegendsAtBottom } from "../utils/legendUtils";
import { getDatavizTheme } from "../common/datavizTheme";
import { useDualAxis } from "../hooks";
import { getDefaultConfig } from "./HorizontalBarsDefaultConfig";
import { type HorizontalBarProps } from "./types";

const HorizontalBars = (props: HorizontalBarProps) => {
  const { data, config, axisKeys, chartId, theme } = props;
  const horizontalBarRef = useRef<HTMLDivElement>(null);

  const mergedConfig = useMemo(
    () => getMergedConfigs(getDefaultConfig(theme), config),
    [theme, config],
  );

  const {
    columnProps,
    roundedColumnProps,
    axisProps,
    gradientProps,
    dualYAxisProps,
    cursorProps,
    legendsProps,
    tooltipProps,
    containerClass,
    bulletProps,
    chartProps,
    columnSetting,
    numberFormatter,
  } = { ...mergedConfig };

  const [xAxisDataKey, yAxisDataKey] = axisKeys;
  const { renderDualAxis } = useDualAxis({ dualYAxisProps });

  useLayoutEffect(() => {
    if (!horizontalBarRef.current) return;

    const root = am5.Root.new(horizontalBarRef.current);
    root.setThemes([
      am5themes_Animated.new(root),
      getDatavizTheme(root, theme),
    ]);

    root.numberFormatter.setAll(numberFormatter ?? {});

    const chart = root.container.children.push(
      am5xy.XYChart.new(root, chartProps?.chartSettings ?? {}),
    );

    const applyContainerSettings = (
      container: am5.Container,
      settings?: any,
    ) => {
      /* eslint-disable @typescript-eslint/no-unsafe-argument */
      if (settings) container.setAll(settings);
    };

    applyContainerSettings(chart.leftAxesContainer, chartProps?.leftContainer);
    applyContainerSettings(
      chart.rightAxesContainer,
      chartProps?.rightContainer,
    );
    applyContainerSettings(chart.chartContainer, chartProps?.chartContainer);
    applyContainerSettings(
      chart.bottomAxesContainer,
      chartProps?.bottomAxesContainer,
    );
    applyContainerSettings(
      chart.topAxesContainer,
      chartProps?.topAxesContainer,
    );
    applyContainerSettings(chart.gridContainer, chartProps?.gridContainer);
    const {
      xAxisSetting,
      yAxisSetting,
      xAxisProps,
      yAxisProps,
      xGridProps = {},
      yGridProps = {},
      xLabelProps = {},
      yLabelProps = {},
      yAxisTitleSetting,
      xAxisTitleSetting,
    } = axisProps ?? {};
    const createRenderer = (
      type: "X" | "Y",
      props: any,
      gridProps: any,
      labelProps: any,
    ) => {
      const renderer =
        type === "X"
          ? am5xy.AxisRendererX.new(root, props)
          : am5xy.AxisRendererY.new(root, props);
      renderer.grid.template.setAll(gridProps);
      renderer.labels.template.setAll({
        ...(labelProps?.labelColor
          ? { fill: am5.color(labelProps.labelColor) }
          : {}),
        ...labelProps,
      });

      return renderer;
    };

    const yRenderer = createRenderer("Y", yAxisProps, yGridProps, yLabelProps);
    const xRenderer = createRenderer("X", xAxisProps, xGridProps, xLabelProps);

    const createTooltip = (setting?: any, backgroundSetting?: any) =>
      am5.Tooltip.new(root, {
        ...(backgroundSetting?.fillColor
          ? {
              background: am5.Rectangle.new(root, {
                fill: am5.color(backgroundSetting.fillColor),
                ...backgroundSetting,
              }),
            }
          : {}),
        ...setting,
      });

    const yAxisTooltip = createTooltip(
      yAxisSetting?.tooltipSetting,
      yAxisSetting?.tooltipBackgroundSetting,
    );
    const xAxisTooltip = createTooltip(
      xAxisSetting?.tooltipSetting,
      xAxisSetting?.tooltipBackgroundSetting,
    );

    const yAxis = chart.yAxes.push(
      am5xy.CategoryAxis.new(root, {
        categoryField: xAxisDataKey,
        renderer: yRenderer,
        tooltip: yAxisTooltip,
        ...yAxisSetting,
      }),
    );

    const xAxis = chart.xAxes.push(
      am5xy.ValueAxis.new(root, {
        renderer: xRenderer,
        tooltip: xAxisTooltip,
        extraMax: bulletProps?.showBullets ? 0.14 : 0,
        ...xAxisSetting,
      }),
    );

    const addAxisTitle = (
      axis: am5xy.Axis<am5xy.AxisRenderer>,
      titleSetting?: any,
    ) => {
      if (titleSetting) {
        axis.children.unshift(
          am5.Label.new(root, {
            y: am5.p50,
            centerX: am5.p50,
            x: am5.p0,
            centerY: am5.p0,
            ...(titleSetting?.labelColor
              ? { fill: am5.color(titleSetting.labelColor) }
              : {}),
            ...titleSetting,
          }),
        );
      }
    };

    addAxisTitle(yAxis, yAxisTitleSetting);
    addAxisTitle(xAxis, xAxisTitleSetting);

    const tooltip = createTooltip(
      tooltipProps?.tooltipSetting,
      tooltipProps?.tooltipBackgroundSetting,
    );

    const series = chart.series.push(
      am5xy.ColumnSeries.new(root, {
        name: "Series 1",
        xAxis,
        yAxis,
        valueXField: yAxisDataKey,
        categoryYField: xAxisDataKey,
        maskBullets: false,
        sequencedInterpolation: true,
        ...(tooltipProps?.showTooltip ? { tooltip } : {}),
        ...columnSetting,
      }),
    );

    series.columns.template.setAll({
      cursorOverStyle: "pointer",
      ...(tooltipProps?.showTooltip
        ? {
            tooltipHTML: tooltipProps.getTooltipHtml?.([
              {
                title: xAxisDataKey,
                key: tooltipProps.tooltipData?.key ?? yAxisDataKey,
                yAxisDataKey,
              },
            ]),
            tooltipX: am5.p100,
          }
        : {}),
      ...columnProps,
      ...roundedColumnProps,
    });

    if (gradientProps?.showGradient) {
      series.columns.template.set(
        "fillGradient",
        am5.LinearGradient.new(root, {
          ...gradientProps,
          stops: gradientProps?.stops.map((stop) => ({
            ...stop,
            color: am5.color(stop.color),
          })),
        }),
      );
    } else {
      series.columns.template.adapters.add("fill", (_, target) => {
        const dataContext = target.dataItem?.dataContext as
          | { color?: string }
          | undefined;
        return dataContext?.color
          ? am5.color(dataContext.color)
          : am5.color(getColorByKey(theme, "scale3")); // fallback color
      });
    }

    if (cursorProps?.showCursor) {
      const cursor = chart.set("cursor", am5xy.XYCursor.new(root, {}));
      cursor.lineY.set("visible", false);
    }

    if (legendsProps?.showLegend) {
      getChartLegendsAtBottom({
        root,
        chart,
        legendsProps,
      });
    }

    yAxis.data.setAll(data);
    series.data.setAll(data);
    void series.appear(1000);

    if (dualYAxisProps?.showDualAxis) renderDualAxis({ root, chart });

    return () => root.dispose();
  }, [data, mergedConfig, xAxisDataKey, yAxisDataKey, renderDualAxis]);

  return (
    <div
      id={chartId}
      className={cn("h-full", containerClass)}
      data-testid={chartId}
      ref={horizontalBarRef}
    />
  );
};

export default HorizontalBars;
