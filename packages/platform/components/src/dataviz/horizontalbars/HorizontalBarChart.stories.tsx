import type { <PERSON>a, StoryObj } from "@storybook/react";
import { Card } from "@zs-nimbus/core";
import HorizontalBar from "./HorizontalBars";
import { barLineData } from "./mock.data";
import { colors } from "@zs-nimbus/foundations";
import * as am5 from "@amcharts/amcharts5";

const meta = {
  title: "dataviz",
  component: HorizontalBar,
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/wLxNEe788V2qdOKQDy31Of/CXO---Desktop-(Analytics)?type=design&node-id=2434-55252&mode=design&t=deBeYFA7WP3acawi-0",
    },
  },
} as Meta<typeof HorizontalBar>;

export default meta;
type Story = StoryObj<typeof meta>;

export const HorizontalBarChart: Story = {
  decorators: [(Story) => <Story />],
};

HorizontalBarChart.decorators = [
  () => (
    <div className="mx-l">
      <Card className="mt-rem-160">
        <Card.Header>HorizontalBarChart</Card.Header>
        <div>
          <HorizontalBar
            {...{
              ...{
                theme: "light",
                data: barLineData,
                axisKeys: ["country", "value"],

                config: {
                  containerClass: "min-h-[260px]",
                  numberFormatter: {
                    numberFormat: "#.#a",

                    bigNumberPrefixes: [
                      { number: 1e3, suffix: "K" },
                      { number: 1e6, suffix: "M" },
                      { number: 1e9, suffix: "B" },
                    ],
                  },
                  chartProps: {
                    chartSettings: {
                      paddingTop: 0,
                      paddingBottom: 0,
                      paddingRight: 0,
                      paddingLeft: 0,
                    },
                  },
                  roundedColumnProps: {
                    cornerRadiusBL: 4,
                    cornerRadiusTL: 4,
                    height: 30,
                    fill: am5.color("#0000FF"),
                  },
                  gradientProps: {
                    showGradient: false,
                    stops: [
                      {
                        color: "rgba(33, 96, 225, 1)",
                        opacity: 1,
                      },
                      {
                        color: "rgba(33, 96, 225, 0.5)",
                        opacity: 0.8,
                      },
                    ],
                    rotation: 0,
                  },
                  tooltipProps: {
                    tooltipBackgroundSetting: {
                      fill: am5.color(colors.light.surface.elevated.low10),
                    },
                    getTooltipHtml: () => `
                          <div class="text-sm text-semantic-content-base-primary">
                              <div class="typography-paragraph1-strong ">Key: {country}</div>    
                              <div class="typography-paragraph1">{value.formatNumber("#.#a")}</div>
                          </div>
                            `,
                  },
                  bulletProps: {
                    showBullets: true,
                    bulletsLabelFormatter: (val: string) => val,
                    bulletColor: colors.light.content.base.primary,
                  },
                  axisProps: {
                    yAxisProps: { minGridDistance: 1 },
                    xAxisProps: { minGridDistance: 20 },
                    xGridProps: { visible: false },
                    yGridProps: { visible: false },
                    xLabelProps: { visible: false },
                    yLabelProps: {
                      centerX: 0,
                      labelColor: colors.light.content.base.primary,
                    },
                  },
                },
              },
            }}
          />
        </div>
      </Card>

      <Card className="mt-rem-160">
        <Card.Header>HorizontalBarChart</Card.Header>
        <div>
          <HorizontalBar
            {...{
              ...{
                theme: "light",
                data: barLineData,
                axisKeys: ["country", "value"],
                config: {
                  containerClass: "h-[300px]",
                  columnProps: {
                    tooltipText: "{country} : {value}",
                    strokeOpacity: 0,
                  },
                  chartProps: {
                    gridContainer: { paddingTop: 40 },
                  },
                  roundedColumnProps: {
                    cornerRadiusTR: 10,
                    cornerRadiusBR: 10,
                    height: 20,
                  },
                  gradientProps: {
                    showGradient: false,
                    stops: [
                      {
                        color: "rgba(33, 96, 225, 1)",
                        opacity: 1,
                      },
                      {
                        color: "rgba(33, 96, 225, 0.5)",
                        opacity: 0.8,
                      },
                    ],
                    rotation: 0,
                  },
                  dualYAxisProps: {
                    showDualAxis: true,
                    labels: [
                      {
                        text: "legends.good",
                        fill: "#ff0000",
                        y: 15,
                      },
                      {
                        text: "legends.okay",
                        fill: "#00ff00",
                        y: 48,
                      },
                      {
                        text: "legends.poor",
                        fill: "#0000ff",
                        y: 82,
                      },
                    ],
                    gradientProps: {
                      stops: [
                        {
                          color: "rgba(61, 165, 146, 1)",
                          opacity: 1,
                          offset: 0,
                        },
                        {
                          color: "rgba(61, 165, 146, 1)",
                          offset: 0.33,
                        },
                        {
                          color: "rgba(255, 255, 225, 1)",
                          offset: 0.33,
                        },
                        {
                          color: "rgba(255, 255, 225, 1)",
                          offset: 0.34,
                        },
                        {
                          color: "rgba(241, 147, 37, 1)",
                          offset: 0.34,
                        },
                        {
                          color: "rgba(241, 147, 37, 1)",
                          offset: 0.66,
                        },
                        {
                          color: "rgba(255, 255, 225, 1)",
                          offset: 0.66,
                        },
                        {
                          color: "rgba(255, 255, 225, 1)",
                          offset: 0.67,
                        },
                        {
                          color: "rgba(220, 54, 46, 1)",
                          offset: 0.67,
                        },
                        {
                          color: "rgba(220, 54, 46, 1)",
                          offset: 1,
                        },
                      ],
                      rotation: 0,
                    },
                  },
                  legendsProps: {
                    showLegend: true,
                    legendMarkerProps: {
                      width: 14,
                      height: 14,
                    },
                    legendLabelProps: {
                      marginLeft: 8,
                      fontSize: 12,
                      fontWeight: "400",
                      legendLabelColor: "rgba(25, 76, 187, 1)",
                    },
                    xLegendPosition: 0,
                    yLegendPosition: 0,
                    legendRectangle: {
                      cornerRadiusTL: 2,
                      cornerRadiusTR: 2,
                      cornerRadiusBR: 2,
                      cornerRadiusBL: 2,
                    },
                  },
                  bulletProps: {
                    bulletColor: "rgba(33, 96, 225, 1)",
                    showBullets: true,
                    bulletsLabelFormatter: function (val: string) {
                      return val + "k";
                    },
                  },
                },
              },
            }}
          />
        </div>
      </Card>

      <Card className="mt-rem-160">
        <Card.Header>HorizontalBarChart Gradient</Card.Header>
        <div>
          <HorizontalBar
            {...{
              ...{
                theme: "dark",
                data: barLineData,
                axisKeys: ["country", "value"],
                config: {
                  containerClass: "h-[550px]",
                  columnProps: {
                    strokeOpacity: 0,
                  },
                  roundedColumnProps: {
                    cornerRadiusTR: 10,
                    cornerRadiusBR: 10,
                  },
                  axisProps: {
                    xAxisProps: { visible: false },
                    xGridProps: {},
                    yAxisProps: {
                      visible: false,
                    },
                    yGridProps: { visible: false },
                    xLabelProps: {
                      labelColor: "#732fe4",
                    },
                  },
                  gradientProps: {
                    showGradient: true,
                    stops: [
                      {
                        color: "#0000ff",
                        opacity: 1,
                      },
                      {
                        color: "#0000ff",
                        opacity: 0.2,
                      },
                    ],
                    rotation: 0,
                  },
                },
              },
            }}
          />
        </div>
      </Card>
    </div>
  ),
];
