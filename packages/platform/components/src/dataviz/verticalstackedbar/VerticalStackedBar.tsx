import { useLayoutEffect, useRef } from "react";
import * as am5 from "@amcharts/amcharts5";
import * as am5xy from "@amcharts/amcharts5/xy";
import am5themes_Animated from "@amcharts/amcharts5/themes/Animated";
import { useTranslation } from "react-i18next";
import { getMergedConfigs } from "../utils";
import { type VerticalStackedBarProps } from "./types";
import { VerticalStackedBarDefaultConfig as defaultConfig } from "./VerticalStackedBarDefaultConfig";
import { getDatavizTheme } from "../hooks";

const VerticalStackedBar = ({
  data,
  dataKey,
  config,
  chartId,
  theme = "light",
}: VerticalStackedBarProps) => {
  const { t } = useTranslation();
  const mergedConfig = getMergedConfigs(
    defaultConfig(theme),
    config?.verticalStackedBarProps,
  );

  const { seriesData, chartData } = data;

  const VerticalStackedBarRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    if (!VerticalStackedBarRef.current) return;

    const {
      chartSettingsProps,
      tooltipProps,
      roundedColumnProps,
      legendsProps,
      legendMarkerProps,
      columnProps,
      xAxisProps,
      xGridProps,
      xAxisLabelProps,
      xAxisGridProps,
      yAxisProps,
      yGridProps,
      yAxisLabelProps,
      yAxisGridProps,
      yAxisTitleSetting,
      numberFormatter,
    } = { ...mergedConfig };
    const { barGap } = yAxisProps ?? {};

    const { legendLabelProps } = legendsProps ?? {};

    const {
      tooltipGraphicsProps,
      tooltipLabelProps,
      tooltipSettingsProps,
      tooltipXPosition,
      tooltipYPosition,
    } = tooltipProps!;

    const root = am5.Root.new(VerticalStackedBarRef.current);

    root.setThemes([
      am5themes_Animated.new(root),
      getDatavizTheme(root, theme),
    ]);
    root.numberFormatter.setAll({
      ...numberFormatter,
    });

    const chart = root.container.children.push(
      am5xy.XYChart.new(root, {
        ...chartSettingsProps,
        layout: root.verticalLayout,
      }),
    );

    const colors = seriesData?.map((s) => am5.color(s.colorCode));
    chart?.get("colors")?.set("colors", colors);

    const xRenderer = am5xy.AxisRendererX.new(root, { ...xAxisGridProps });
    xRenderer.labels.template.setAll({
      ...(xAxisLabelProps?.labelColor
        ? { fill: am5.color(xAxisLabelProps.labelColor) }
        : {}),
      ...xAxisLabelProps,
    });
    const xAxis = chart.xAxes.push(
      am5xy.CategoryAxis.new(root, {
        ...xAxisProps,
        categoryField: dataKey,
        renderer: xRenderer,
      }),
    );
    xRenderer.grid.template.setAll({ ...xGridProps });

    xAxis.data.setAll(chartData);

    const yRenderer = am5xy.AxisRendererY.new(root, { ...yAxisGridProps });
    yRenderer.labels.template.setAll({
      ...(yAxisLabelProps?.labelColor
        ? { fill: am5.color(yAxisLabelProps.labelColor) }
        : {}),
      ...yAxisLabelProps,
    });
    yRenderer.grid.template.setAll({
      ...yGridProps,
    });
    const yAxis = chart.yAxes.push(
      am5xy.ValueAxis.new(root, {
        ...yAxisProps,
        renderer: yRenderer,
      }),
    );

    if (yAxisTitleSetting) {
      const yAxisTitle = am5.Label.new(root, {
        y: am5.p50,
        centerX: am5.p50,
        x: am5.p0,
        centerY: am5.p0,
        ...(yAxisTitleSetting?.labelColor
          ? { fill: am5.color(yAxisTitleSetting?.labelColor) }
          : {}),
        ...yAxisTitleSetting,
      });
      yAxis.children.unshift(yAxisTitle);
    }

    const legend = chart.children.push(
      am5.Legend.new(root, {
        ...legendsProps,
        centerX: am5.p50,
        x: am5.p50,
      }),
    );
    legend.labels.template.setAll({
      ...(legendLabelProps?.legendLabelColor
        ? { fill: am5.color(legendLabelProps.legendLabelColor) }
        : {}),
      ...legendLabelProps,
    });
    legend.markers.template.setAll({
      ...legendMarkerProps,
    });

    legend.valueLabels.template.set("forceHidden", true);

    function makeSeries(name: string, fieldName: string, index: number) {
      const series = chart.series.push(
        am5xy.ColumnSeries.new(root, {
          name: name,
          stacked: true,
          xAxis: xAxis,
          yAxis: yAxis,
          valueYField: fieldName,
          categoryXField: dataKey,
        }),
      );

      series.columns.template.adapters.add("cornerRadiusTL", cornerRadius);
      series.columns.template.adapters.add("cornerRadiusTR", cornerRadius);

      if (barGap) {
        series.columns.template.adapters.add(
          "dy",
          (_: number | undefined, item: am5.RoundedRectangle) => {
            const { dataContext } = item.dataItem ?? {};
            const dataContextTyped = dataContext as Record<string, unknown>;

            let lowerValueExists = 0;
            for (let i = 0; i < index; i++) {
              if (dataContextTyped[seriesData?.[i]!.propertyName]) {
                lowerValueExists += 1;
              }
            }
            const isDataAvailable = !!dataContextTyped[fieldName];

            if (!index || !lowerValueExists) {
              return 0;
            }

            return isDataAvailable
              ? lowerValueExists * barGap
              : (lowerValueExists - 1) * barGap;
          },
        );
      }

      const tooltip = am5.Tooltip.new(root, {
        ...tooltipSettingsProps,
      });

      tooltip.get("background")?.setAll({
        ...tooltipGraphicsProps,
      });

      series.columns.template.setAll({
        ...columnProps,
        tooltip,
        tooltipX: am5.percent(tooltipXPosition),
        tooltipY: am5.percent(tooltipYPosition),
      });

      series.data.setAll(chartData);

      void series.appear();

      legend.data.push(series);
    }

    function cornerRadius(
      radius: number | undefined,
      item: am5.RoundedRectangle,
    ) {
      if (item.dataItem?.dataContext) {
        const { dataContext, component } = item.dataItem;
        let lastSeries;
        chart.series.each(function (series: am5xy.XYSeries) {
          const { valueYField } = series._settings as { valueYField: string };
          const dataContextTyped = dataContext as Record<string, unknown>;
          if (dataContextTyped[valueYField]) {
            lastSeries = series;
          }
        });

        return component == lastSeries
          ? (roundedColumnProps?.cornerRadiusTL ?? 5)
          : radius;
      }
    }

    seriesData?.forEach((d, index) => {
      makeSeries(t(d.label), d.propertyName, index);
    });

    void chart.appear(100, 100);

    return () => root.dispose();
  }, [seriesData, chartData, dataKey, t]);

  return (
    <div
      className={mergedConfig.containerClass}
      data-testid={chartId}
      ref={VerticalStackedBarRef}
    />
  );
};

export default VerticalStackedBar;
