import { colors } from "@zs-nimbus/foundations";
import { type ThemeType } from "../common/types";
import { type VerticalStackedBarDefaultProps } from "./types";

export const VerticalStackedBarDefaultConfig = (
  theme: ThemeType,
): VerticalStackedBarDefaultProps => {
  return {
    containerClass: "flex h-[400px]",
    columnProps: {
      width: 50,
      tooltipHTML: `<span>{name}</span>`,
    },
    chartSettingsProps: {
      panX: false,
      panY: false,
      paddingLeft: 0,
      wheelX: "none",
      wheelY: "none",
    },
    xAxisGridProps: { minGridDistance: 10 },
    legendsProps: {
      legendLabelProps: {
        fontSize: 12,
        fontWeight: "400",
      },
    },
    tooltipProps: {
      tooltipSettingsProps: {
        getLabelFillFromSprite: false,
        pointerOrientation: "vertical",
      },
      tooltipLabelProps: {
        fontSize: 12,
      },
      tooltipXPosition: 0,
      tooltipYPosition: 0,
    },
  };
};
