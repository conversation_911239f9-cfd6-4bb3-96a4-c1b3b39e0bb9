import type { <PERSON>a, StoryObj } from "@storybook/react";
import { Card } from "@zs-nimbus/core";
import VerticalStackedBar from "./VerticalStackedBar";

const meta = {
  title: "dataviz",
  component: VerticalStackedBar,
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/wLxNEe788V2qdOKQDy31Of/CXO---Desktop-(Analytics)?type=design&node-id=2434-55252&mode=design&t=deBeYFA7WP3acawi-0",
    },
  },
} as Meta<typeof VerticalStackedBar>;

export default meta;
type Story = StoryObj<typeof meta>;

export const VerticalStackedBarChart: Story = {
  decorators: [(Story) => <Story />],
};

VerticalStackedBarChart.decorators = [
  () => (
    <div className="mx-l">
      <Card className="mt-rem-160">
        <Card.Header>VerticalStackedBarChart</Card.Header>
        <div>
          <VerticalStackedBar
            {...{
              ...{
                theme: "light",
                data: {
                  chartData: [
                    {
                      id: "Allow & Scan",
                      malicious: 40,
                      benign: 20,
                    },
                    {
                      id: "Quarantine",
                      malicious: 10,
                      benign: 10,
                    },
                    {
                      id: "Allow/Do Not Scan",
                      malicious: 10,
                    },
                  ],
                  seriesData: [
                    {
                      label: "Malicious",
                      propertyName: "malicious",
                      colorCode: "rgba(220, 54, 46, 1)",
                    },
                    {
                      label: "Benign",
                      propertyName: "benign",
                      colorCode: "rgba(98, 171, 87, 1)",
                    },
                  ],
                },
                dataKey: "id",
                config: {
                  verticalStackedBarProps: {
                    legendsProps: {
                      legendSetting: {
                        clickTarget: "none",
                        paddingTop: 10,
                      },
                    },
                    legendMarkerProps: {
                      width: 12,
                      height: 12,
                    },
                    xAxisLabelProps: {
                      fontSize: 12,
                      paddingTop: 8,
                    },
                    xGridProps: { visible: false },
                    yAxisLabelProps: {
                      fontSize: 12,
                    },
                    yGridProps: { visible: true },
                    yAxisProps: {
                      visible: true,
                      maxPrecision: 0,
                      max: 135,
                    },
                    yAxisGridProps: {
                      minGridDistance: 60,
                    },
                    columnProps: {
                      width: 60,
                      tooltipHTML: `<div>
            <div class="font-bold text-[14px] text-grey-900 mb-[2px]">{name}</div>
            <div class="font-light text-[13px]">{valueY}</div>
          </div>`,
                      cursorOverStyle: "pointer",
                    },
                    tooltipProps: {
                      tooltipXPosition: 50,
                      tooltipYPosition: 50,
                      tooltipGraphicsProps: {
                        fillOpacity: 1,
                        shadowBlur: 10,
                        strokeWidth: 1,
                      },
                      tooltipSettingsProps: {
                        getFillFromSprite: false,
                        autoTextColor: false,
                        pointerOrientation: "left",
                      },
                    },
                  },
                },
              },
            }}
          />
        </div>
      </Card>
    </div>
  ),
];
