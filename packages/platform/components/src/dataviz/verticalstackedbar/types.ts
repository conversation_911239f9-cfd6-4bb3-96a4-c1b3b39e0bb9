import type * as am5 from "@amcharts/amcharts5";
import type * as am5xy from "@amcharts/amcharts5/xy";
import { AxisLabelProps, LegendsProps, type ThemeType } from "../common/types";

export type Series = {
  label: string;
  propertyName: string;
  colorCode: string;
};

export type Data = {
  id: string;
  malicious?: number;
  benign?: number;
  okay?: number;
  good?: number;
  poor?: number;
};

export type StackedBarTooltip = {
  tooltipSettingsProps?: am5.ITooltipSettings;
  tooltipGraphicsProps?: am5.IGraphicsSettings;
  tooltipLabelProps?: am5.ILabelSettings;
  tooltipXPosition: number;
  tooltipYPosition: number;
};

export type VerticalStackedBarDefaultProps = {
  containerClass?: string;
  tooltipProps: StackedBarTooltip;
  roundedColumnProps?: am5.IRoundedRectangleSettings;
  chartSettingsProps?: am5xy.IXYChartSettings;
  columnProps?: am5.IRoundedRectangleSettings;
  legendsProps?: LegendsProps;
  legendMarkerProps?: am5.IContainerSettings;
  legendLabelProps?: am5.ILabelSettings;
  xAxisLabelProps?: AxisLabelProps;
  xGridProps?: am5xy.IGridSettings;
  xAxisProps?: Omit<
    am5xy.ICategoryAxisSettings<am5xy.AxisRenderer>,
    "categoryField" | "renderer"
  >;
  xAxisGridProps?: am5xy.IAxisRendererXSettings;
  yAxisLabelProps?: AxisLabelProps;
  yGridProps?: am5xy.IGridSettings;
  yAxisProps?: Omit<
    am5xy.IValueAxisSettings<am5xy.AxisRenderer>,
    "renderer"
  > & { barGap?: number };
  yAxisGridProps?: am5xy.IAxisRendererYSettings;
  yAxisTitleSetting?: am5.ILabelSettings & { labelColor?: string };
  numberFormatter?: am5.INumberFormatterSettings;
};

export type VerticalStackedBarConfig = {
  verticalStackedBarProps: Omit<
    VerticalStackedBarDefaultProps,
    "tooltipProps" | "chartColors"
  > & {
    tooltipProps?: StackedBarTooltip;
  };
};

export type VerticalStackedBarProps = {
  data: {
    chartData: Data[];
    seriesData: Series[];
  };
  dataKey: string;
  config: VerticalStackedBarConfig;
  chartId?: string;
  theme?: ThemeType;
};
