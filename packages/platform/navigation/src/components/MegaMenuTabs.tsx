import { type PropsWithChildren } from "react";
import { cn } from "@up/components";
import { props, restProps, testIdFrom } from "@up/std";
import { filterHiddenNodes, Layer, type TreeNode } from "../core";
import { useNavigationContext, useNavigationStore } from "../context";
import { type BaseNavigationProps } from "./types";
import { handleKeyboardDown } from "./events";

type Props<T extends object> = {
  active?: TreeNode<T>;
  selectedTab?: TreeNode<T>;
  current?: TreeNode<T>;
  onItemClicked: (node?: TreeNode<T>) => void;
} & BaseNavigationProps<T>;

export function MegaMenuTabs<T extends object>({
  active = undefined,
  selectedTab = undefined,
  current = undefined,
  onItemClicked,
  entitlements,
  ...rest
}: PropsWithChildren<Props<T>>): JSX.Element {
  const { store, selectedLayerNode, inferCorrectNodeAlt } =
    useNavigationStore<T>();
  const { t } = useNavigationContext();

  const resolvedNode = selectedTab;

  const inferred = inferCorrectNodeAlt(
    resolvedNode ?? current ?? active ?? "",
    Layer.Secondary,
    entitlements,
  );

  let tabs: Array<TreeNode<T>> = [];

  if (!store.tree.isContainer(inferred!)) {
    tabs = store.tree.layer(Layer.Tertiary, inferred);
    if (store.tree.isContainer(tabs[0] ?? "")) {
      tabs = [];
    }
  }

  if (entitlements) {
    tabs = tabs.filter((n: TreeNode<T>) => {
      return !filterHiddenNodes(n, entitlements);
    });
  }

  let selectedVTab = active ? active.id : "";

  const inferredChildren = store.tree.resolveChildren(inferred ?? "");
  const containsActive = inferredChildren?.find(
    (child) => child.id === active?.id,
  );

  if (!active || !containsActive) {
    const svtab = selectedLayerNode(Layer.Tertiary);

    const matchingTab = tabs.find((tab) => tab.id === svtab?.id);
    if (tabs.length > 0) {
      selectedVTab = matchingTab ? matchingTab.id : (tabs[0]?.id ?? "");
    }
  }

  const baseClasses =
    "p-3 text-[13px] cursor-pointer leading-5 w-[224px] flex justify-between items-center rounded-80 text-semantic-content-base-tertiary  font-medium";

  const inactiveClasses =
    "hover:bg-semantic-surface-fields-hover hover:text-semantic-content-base-primary";
  const activeClasses =
    "bg-semantic-surface-fields-active text-semantic-content-interactive-primary-default";

  const wrapperClasses = cn(
    "flex flex-col pr-l border-r border-r-semantic-border-base-subdued mt-rem-160",
  );

  return (
    <div className={tabs?.length === 0 ? "flex pl-[240px]" : ""}>
      {tabs?.length > 0 && (
        <div
          {...restProps(rest)}
          className={wrapperClasses}
          id="mega-menu-tabs"
        >
          <div
            className="flex flex-col min-w-[180px]"
            data-testid="mega-menu-tabs-container"
          >
            {tabs.map((n: TreeNode<T>, idx: number) => {
              const clist = cn(
                baseClasses,
                selectedVTab == n.id ? activeClasses : inactiveClasses,
              );

              return (
                <div key={`mg-menu-tab-${idx}`}>
                  <button
                    type="button"
                    className={clist}
                    onKeyDown={handleKeyboardDown(() => {
                      onItemClicked(n);
                    })}
                    onClick={() => onItemClicked(n)}
                    {...props({
                      id: rest.id,
                      testId: testIdFrom(rest, ["vertical-tab", idx]),
                    })}
                    tabIndex={0}
                  >
                    {t(n.option.key)}
                    <i
                      aria-label={t("EXPAND_MENU")}
                      className="fa fa-angle-right text-semantic-content-interactive-primary-default"
                    />
                  </button>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
