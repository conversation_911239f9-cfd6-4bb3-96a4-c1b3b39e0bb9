import type React from "react";
import { useCallback, useContext, useMemo } from "react";
import {
  TableContainer,
  TableCellContainer,
} from "@zscaler/zui-component-library";

import { noop } from "lodash-es";

import { CRUDPageContext } from "../../../contexts/CRUDPageContextProvider";
import {
  type GetListParams,
  type TableColumnDetail,
} from "../../../types/table";
import { useSubjectIdentifierContext } from "../../../ducks/subject-identifier";
import { useSubjectIdentifierSelectors } from "../../../ducks/subject-identifier/selectors";
import { useApiCall } from "../../../hooks/useApiCallContext";

type RowData = {
  subjectName?: string;
  id: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
};

const Table: React.FC = () => {
  const { apiCall } = useApiCall();

  const { setModalMode, setDetail, detail } = useContext(CRUDPageContext)!;
  const { getList } = useSubjectIdentifierContext();
  const { tableConfig, tableDetail } = useSubjectIdentifierSelectors();

  const onEditClick = useCallback(
    (detail: RowData) => {
      if (detail) {
        setDetail(detail);
        setModalMode(true);
      }
    },
    [setDetail, setModalMode],
  );

  const renderCellContainer = useCallback(
    ({
      column,
      getContext,
      row,
    }: {
      column: any;
      getContext: any;
      row: any;
    }) => {
      const isSelected = row?.id === detail?.id;

      return (
        <div
          className={
            isSelected ? "border-l-8 border-r-2 border-t-2 border-b-2 pl-2" : ""
          }
          onClick={() => {
            onEditClick({ ...row?.original, id: row?.id });
          }}
          style={{
            borderColor: "#2160E1",
            cursor: "pointer",
            height: "100%",
            width: "100%",
          }}
        >
          {TableCellContainer({ column, getContext })}
        </div>
      );
    },
    [detail, onEditClick],
  );

  const tableColumnConfig = useMemo(
    () =>
      tableConfig?.columns?.map((columnDetail: TableColumnDetail) => {
        return columnDetail;
      }) || [],
    [tableConfig?.columns],
  );

  const onLoadMoreClick = () => {
    const { pageOffset } = tableDetail;

    // API returns ~10 records per page, so increment by 10
    const actualRecordsPerPage = 10;
    const requestedPageSize = 50; // Keep requesting 50 but expect 10

    const payload: GetListParams = {
      pageSize: requestedPageSize,
      pageOffset: (pageOffset || 0) + actualRecordsPerPage,
    };

    apiCall(() => getList(payload)).catch(noop);
  };

  return (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={tableDetail.data}
      pagination={{ ...tableDetail, onLoadMoreClick }}
      renderCellContainer={renderCellContainer}
    />
  );
};

export default Table;
