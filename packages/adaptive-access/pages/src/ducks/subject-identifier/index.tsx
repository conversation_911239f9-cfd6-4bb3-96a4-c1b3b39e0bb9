"use client";

import React, {
  createContext,
  useContext,
  useReducer,
  useCallback,
  type ReactNode,
} from "react";
import { http } from "../../utils/http";
import {
  API_ENDPOINT,
  DATA_TABLE_DETAIL,
  DATA_INNER_TABLE_DETAIL,
  DEFAULT_STATE,
  type DefaultState,
} from "./constants";
import type { GetListParams } from "../../types/table";
import { AxiosResponse } from "axios";

export type UpdateListPayload = {
  overrideList: Array<Record<string, unknown>>;
  totalEntries: number;
  currentPage: number;
  pageSize: number;
};

export type GetInnerListParams = {
  subjectType?: string;
  subjectId?: string;
  subjectName?: string;
  contextCount?: number;
  overrideCount?: number;
  isEmpty?: boolean;
};

type ApiResponse = {
  data?: Array<Record<string, unknown>>;
};

export type TypePayload = {
  tenantId?: string;
  subjectType?: string;
  subjectId?: string;
  subjectIdentifier?: string;
  contextType?: string;
  source?: string;
  subjectName?: string;
  userId?: string;
  sortkey?: string;
  summarySortkey?: string;
  isSystemRole?: boolean;
  isOverridable?: boolean;
  isFormValid?: boolean;
  [key: string]: any;
};

type SubjectIdentifierState = DefaultState;

type SubjectIdentifierAction =
  | {
      type: "UPDATE_TABLE_DETAIL";
      payload: {
        data: Array<Record<string, unknown>>;
        pageSize?: number;
        pageOffset?: number;
        totalEntries?: number;
        actualRecordsPerPage?: number;
      }
    }
  | {
      type: "UPDATE_INNER_TABLE_DETAIL";
      payload: Array<Record<string, unknown>>;
    };

const SubjectIdentifierContext = createContext<
  | {
      state: SubjectIdentifierState;
      getList: (params?: GetListParams) => Promise<void>;
      getInnerList: (params?: GetInnerListParams) => Promise<void>;
      addRow: (payload: Record<string, unknown>) => Promise<void>;
      deleteRow: (payload: Record<string, unknown>) => Promise<void>;
      updateRow: (payload: Record<string, unknown>) => Promise<void>;
    }
  | undefined
>(undefined);

const subjectIdentifierReducer = (
  state: SubjectIdentifierState,
  action: SubjectIdentifierAction,
): SubjectIdentifierState => {
  switch (action.type) {
    case "UPDATE_TABLE_DETAIL": {
      const { data, pageSize, pageOffset, totalEntries, actualRecordsPerPage } = action.payload;
      const tableDetail = { ...state[DATA_TABLE_DETAIL] };

      tableDetail.error = {};
      tableDetail.hasError = false;
      tableDetail.hasData = true;
      tableDetail.pageSize = pageSize; // Keep API pageSize for display
      tableDetail.pageOffset = pageOffset;

      let totalResult: Array<Record<string, unknown>> = [];

      // If pageOffset is 0 or undefined, it's a fresh load (replace data)
      // Otherwise, it's a load more (append data)
      if (pageOffset) {
        totalResult = [...tableDetail.data, ...data];
      } else {
        totalResult = [...data];
      }

      tableDetail.data = totalResult;

      // Check if we've fetched all records
      // Compare current data length with total entries from API
      if (totalEntries && totalResult.length >= totalEntries) {
        tableDetail.hasFetchedAllRecords = true;
      } else {
        tableDetail.hasFetchedAllRecords = false;
      }

      // Update total record from API response
      tableDetail.totalRecord = totalEntries || totalResult.length;

      // Store actual records per page for pagination calculations
      if (actualRecordsPerPage) {
        tableDetail.actualRecordsPerPage = actualRecordsPerPage;
      }

      return {
        ...state,
        [DATA_TABLE_DETAIL]: tableDetail,
      };
    }
    case "UPDATE_INNER_TABLE_DETAIL": {
      return {
        ...state,
        [DATA_INNER_TABLE_DETAIL]: {
          ...state[DATA_INNER_TABLE_DETAIL],
          data: [...action.payload],
          totalRecord: action.payload.length,
          hasFetchedAllRecords: true,
        },
      };
    }
    default:
      return state;
  }
};

export const SubjectIdentifierProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(subjectIdentifierReducer, DEFAULT_STATE);

  const getList = useCallback(async (params?: GetListParams) => {
    const {
      pageSize = DEFAULT_STATE[DATA_TABLE_DETAIL].pageSize,
      pageOffset = DEFAULT_STATE[DATA_TABLE_DETAIL].pageOffset,
      name,
    } = params || {};

    // Calculate page number (backend expects page starting from 1)
    // Use stored actualRecordsPerPage or fallback to a reasonable default
    const currentTableDetail = state[DATA_TABLE_DETAIL];
    const actualRecordsPerPage = currentTableDetail.actualRecordsPerPage || 10;
    const page = Math.floor((pageOffset || 0) / actualRecordsPerPage) + 1;

    let requestUrl = `${API_ENDPOINT}/summary/subject?pageSize=${pageSize}&page=${page}`;

    if (name) {
      requestUrl += `&subjectName=${encodeURIComponent(name.trim())}`;
    }

    const response: AxiosResponse<UpdateListPayload> =
      await http.get(requestUrl);

    if (response?.data?.overrideList) {
      dispatch({
        type: "UPDATE_TABLE_DETAIL",
        payload: {
          data: response.data.overrideList || [],
          pageSize: response.data.pageSize, // Use pageSize from API response for display
          pageOffset,
          totalEntries: response.data.totalEntries,
          actualRecordsPerPage: response.data.overrideList.length, // Track actual records returned
        },
      });
    }
  }, []);

  const getInnerList = useCallback(
    async ({
      subjectType = "",
      subjectId = "",
      isEmpty = false,
    }: GetInnerListParams = {}) => {
      if (isEmpty) {
        dispatch({ type: "UPDATE_INNER_TABLE_DETAIL", payload: [] });
        return;
      }

      const requestUrl = `${API_ENDPOINT}/subjects/${subjectType}/${subjectId}`;
      const response: AxiosResponse<ApiResponse[]> = await http.get(requestUrl);

      if (response?.data) {
        dispatch({
          type: "UPDATE_INNER_TABLE_DETAIL",
          payload: response?.data || [],
        });
      }
    },
    [],
  );

  const addRow = useCallback(
    async (payload: TypePayload) => {
      const response = await http.post(API_ENDPOINT, payload);

      if (response?.data) {
        await getList();
      }
    },
    [getList],
  );

  const deleteRow = useCallback(
    async (payload: TypePayload) => {
      const subjectId = payload.subjectId || payload.subjectIdentifier;

      const deletePayload = {
        tenantId: payload.tenantId,
        subjectType: payload.subjectType,
        subjectId: subjectId,
        contextType: payload.contextType,
        source: payload.source,
        subjectName: payload.subjectName,
        ...(payload.subjectType === "USER_DEVICE"
          ? { userId: payload.userId }
          : {}),
      };

      await http.delete(API_ENDPOINT, { data: deletePayload });

      await getInnerList({
        subjectType: payload.subjectType,
        subjectId: subjectId,
      });
    },
    [getInnerList],
  );

  const updateRow = useCallback(
    async (payload: TypePayload) => {
      const updatedPayload = { ...payload };
      delete updatedPayload.sortkey;
      delete updatedPayload.summarySortkey;

      const subjectId =
        updatedPayload.subjectId || updatedPayload.subjectIdentifier;

      updatedPayload.subjectId = subjectId;

      const response = await http.post(API_ENDPOINT, updatedPayload);

      if (response?.data) {
        await getInnerList({
          subjectType: payload.subjectType,
          subjectId: subjectId,
        });
      }
    },
    [getInnerList],
  );

  return (
    <SubjectIdentifierContext.Provider
      value={{
        state,
        getList,
        getInnerList,
        addRow,
        deleteRow,
        updateRow,
      }}
    >
      {children}
    </SubjectIdentifierContext.Provider>
  );
};

export const useSubjectIdentifierContext = () => {
  const context = useContext(SubjectIdentifierContext);

  if (!context) {
    throw new Error(
      "useSubjectIdentifierContext must be used within SubjectIdentifierProvider",
    );
  }

  return context;
};
