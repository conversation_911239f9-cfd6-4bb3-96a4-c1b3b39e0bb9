{"name": "@zuxp/copilot", "version": "0.0.1", "type": "module", "files": ["dist"], "main": "./dist/index.cjs", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "./dist/styles.css": {"import": "./dist/styles.css", "require": "./dist/styles.css"}}, "scripts": {"dev": "vite", "build": "tsc && vite build && pnpm build-tailwind", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives", "prettier:config": "prettier **/*.{md,json,css}", "prettier": "pnpm prettier:config --check", "verify": "pnpm prettier && pnpm lint --max-warnings=0", "preview": "vite preview", "build-tailwind": "tailwindcss -i ./src/global.css -o ./dist/styles.css", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@floating-ui/react": "0.26.24", "@fortawesome/fontawesome-svg-core": "6.6.0", "@fortawesome/pro-regular-svg-icons": "6.2.0", "@fortawesome/pro-solid-svg-icons": "6.2.0", "@fortawesome/react-fontawesome": "0.2.2", "classnames": "2.5.1", "react": "18.3.1", "react-dom": "18.3.1"}, "devDependencies": {"@storybook/addon-essentials": "8.3.5", "@storybook/react": "8.3.5", "@storybook/react-vite": "8.3.5", "@types/react": "18.3.12", "@types/react-dom": "18.3.0", "@types/react-fontawesome": "1.6.8", "@typescript-eslint/eslint-plugin": "8.8.0", "@typescript-eslint/parser": "8.8.0", "@vitejs/plugin-react": "catalog:vite", "autoprefixer": "10.4.20", "eslint": "8.57.1", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-typescript": "3.6.3", "eslint-plugin-import": "2.31.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "7.37.1", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-react-refresh": "0.4.12", "eslint-plugin-storybook": "0.9.0", "storybook": "8.3.5", "tailwindcss": "3.4.13", "typescript": "5.6.2", "typescript-eslint": "8.8.0", "vite": "catalog:vite", "vite-plugin-dts": "catalog:vite"}}