{"name": "@zuxp/nimbus-components", "version": "0.0.15", "type": "module", "files": ["dist"], "main": "./dist/main.cjs", "module": "./dist/main.js", "exports": {".": {"import": "./dist/main.js", "require": "./dist/main.cjs"}, "./dist/styles.css": {"import": "./dist/styles.css", "require": "./dist/styles.css"}, "./tailwind": {"import": "./dist/tailwind.config.js", "require": "./dist/tailwind.config.cjs"}}, "scripts": {"dev": "vite", "build": "tsc && vite build && pnpm build-tailwind", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives", "prettier": "pnpm prettier:config --check", "prettier:config": "prettier  './lib/**/*.{js,jsx,ts,tsx}'", "format": "pnpm prettier:config --write", "verify": "pnpm prettier && pnpm lint --max-warnings=0", "preview": "vite preview", "build-tailwind": "tailwindcss -i ./lib/global.css -o ./dist/styles.css", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "lost-pixel": "LOST_PIXEL_DISABLE_TELEMETRY=1 lost-pixel", "lost-pixel:update": "LOST_PIXEL_DISABLE_TELEMETRY=1 lost-pixel update --configDir ./.lostpixel/config"}, "dependencies": {"@floating-ui/react": "0.26.24", "@fortawesome/fontawesome-svg-core": "6.6.0", "@fortawesome/pro-regular-svg-icons": "6.2.0", "@fortawesome/react-fontawesome": "0.2.2", "classnames": "2.5.1", "react": "18.3.1", "react-dom": "18.3.1"}, "devDependencies": {"@storybook/addon-essentials": "8.6.0", "@storybook/react": "8.6.0", "@storybook/react-vite": "8.6.0", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@types/react-fontawesome": "1.6.8", "@typescript-eslint/eslint-plugin": "8.25.0", "@typescript-eslint/parser": "8.25.0", "@vitejs/plugin-react": "catalog:vite", "eslint": "8.57.1", "eslint-config-prettier": "10.0.2", "eslint-import-resolver-typescript": "3.7.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-prettier": "5.2.3", "eslint-plugin-react": "7.37.4", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-react-refresh": "0.4.16", "eslint-plugin-storybook": "0.11.3", "lost-pixel": "3.22.0", "prettier": "3.5.2", "storybook": "8.6.0", "tailwindcss": "3.4.17", "typescript": "5.7.3", "typescript-eslint": "8.25.0", "vite": "catalog:vite", "vite-plugin-dts": "catalog:vite"}, "volta": {"extends": "../../../package.json"}, "prettier": {"singleQuote": false, "arrowParens": "always", "bracketSameLine": false, "endOfLine": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": false, "printWidth": 80, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "semi": true, "tabWidth": 2, "trailingComma": "all"}}