@ZIA_admin @Ipv6Configuration @test-priyanshi
Feature: To verify the ipv6 configuration functionality

  Scenario: To verify the Enable IPv6 toggle
   Given User navigates to the IPv6 Configuration screen
   When User verify the heading "IPv6 Configuration" for IPv6
   And Verify the title "Enable IPv6"
   Then Change the Enable IPv6 toggle for IPv6
   And User clicks on "Save" button for IPv6
   Then Enable the Enable IPv6 toggle for IPv6
   And User clicks on "Save" button for IPv6

  Scenario:To navigate to NAT64 Prefixes Tab
    Given User navigates to the IPv6 Configuration screen
    When User verify the heading "IPv6 Configuration" for IPv6
    And Verify the title "NAT64 Prefixes"

  
  Scenario:To navigate to DNS64 Prefix Tab
    Given User navigates to the IPv6 Configuration screen
    When User verify the heading "IPv6 Configuration" for IPv6
    And Verify the title "DNS64 Prefix"

  Scenario:To verify the Add and Search Functionality of NAT64 Prefixes
    Given User navigates to the IPv6 Configuration screen
    When User verify the heading "IPv6 Configuration" for IPv6
    Then Navigate to the "NAT64 Prefixes" tab for IPv6
    Then Verify the title "NAT64 Prefixes"
    Then User adds the data with NAT "2345:5678:6300::"
    Then User search the data
 
  Scenario:To verify the Edit Functionality of NAT64 Prefixes
    Given User navigates to the IPv6 Configuration screen
    When User verify the heading "IPv6 Configuration" for IPv6
    Then Navigate to the "NAT64 Prefixes" tab for IPv6
    Then Verify the title "NAT64 Prefixes"
    Then User edits the data with NAT "2345:5578:6700::"
    Then User search the data

  
  Scenario:To verify the Delete Functionality of NAT64 Prefixes
    Given User navigates to the IPv6 Configuration screen
    When User verify the heading "IPv6 Configuration" for IPv6
    Then Navigate to the "NAT64 Prefixes" tab for IPv6
    Then Verify the title "NAT64 Prefixes"
    Then User deletes the data
    

 

  