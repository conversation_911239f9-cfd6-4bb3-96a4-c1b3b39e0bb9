import { createBdd } from 'playwright-bdd';
const { Given, When, Then } = createBdd();
import Ipv6Configuration from '../../pages/Ipv6Configuration/ipv6Configuration_page';

Given('User navigates to the IPv6 Configuration screen', async({page}) => {
    await Ipv6Configuration.navigateToIpv6Configuration(page);
})

When('User verify the heading {string} for IPv6', async({page}, heading: string) => {
    await Ipv6Configuration.verifyHeading(page, heading);
})

Then('Change the Enable IPv6 toggle for IPv6', async({page}) => {
    await Ipv6Configuration.toggleEnableIpv6(page);
})

Then('Enable the Enable IPv6 toggle for IPv6', async({page}) => {
    await Ipv6Configuration.enableIpv6Configuration(page);
})

Then('User clicks on {string} button for IPv6', async({page}, button: string) => {
    await Ipv6Configuration.clickButton(page, button);
})

Then('Navigate to the {string} tab for IPv6', async({page}, button: string) => {
     await Ipv6Configuration.navigateToTab(page, button);
})

Then('User adds the data with NAT {string}', async({page}, natPrefix: string) => {
     await Ipv6Configuration.addNat64Prefix(page, 'test nat prefix', natPrefix, '/40');
})

Then('User edits the data with NAT {string}', async({page}, editedPrefix: string) => {
     await Ipv6Configuration.editNat64Prefix(page, 'Edited test nat prefix',editedPrefix, 'description added to test');
})

Then('User search the data', async({page}, button: string) => {
     await Ipv6Configuration.searchNat64Prefix(page, 'test nat prefix');
})

Then('User deletes the data', async({page}, button: string) => {
     await Ipv6Configuration.deleteNat64Prefix(page, 'test nat prefix');
})

