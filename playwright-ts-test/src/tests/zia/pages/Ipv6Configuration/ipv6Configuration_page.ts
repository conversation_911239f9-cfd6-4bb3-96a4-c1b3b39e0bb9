import { expect, Page } from '@playwright/test';
import { config } from 'dotenv';

config(); 

const url: string = process.env.ONE_UI_BASE_URL ?? "";

const Selectors: Record<string, string> = {
   Save: "-js-save button big primary",
   Cancel: "-js-cancel button big no-bkgrd"
};

class Ipv6Configuration {
    fields: {
        spanHeadings: (heading: string, classButton: string) => string;
         tabHeadings: (tabName: string, classTab: string) => string;
    };

    constructor() {
        this.fields = {
          spanHeadings: (heading, classButton) => `//span[text()='${heading}' and @class='${classButton}']`,
          tabHeadings: (tabName) => `//span[text()='${tabName}' and @class='content-tab-title']`,
        };
    }

    async navigateToIpv6Configuration(page: Page): Promise<void>{
          console.log("The Url used is : - "+url);
            if (!url) {
              throw new Error('ONE_UI_BASE_URL environment variable is not set');
            }
            if (url.includes("console")) {
              await page.goto(url+"internet-saas#administration/ipv6-configuration");
              await page.waitForTimeout(15000);
              }
            else{
              await page.goto(url+"#administration/ipv6-configuration");
              await page.waitForTimeout(19000);
            }
    }

    async verifyHeading(page: Page, heading: string): Promise<void>{
        await page.getByText('IpV6 Configuration', { exact: true }).isVisible();
        const value = await page.locator(`//span[text()="${heading}" and @class='-js-title-text']`);
        expect(value).toHaveText(heading);
    }

    async toggleEnableIpv6(page: Page): Promise<void>{
        await page.locator('.toggle-button').first().isVisible();
        await page.locator('.toggle-button').first().click();
        await page.waitForTimeout(2000);
    }

    async enableIpv6Configuration(page: Page): Promise<void>{
      await page.waitForTimeout(3000);
      await page.locator("(//span[@class='toggle-label off'])").first().isVisible();
      await page.locator("(//span[@class='toggle-label off'])").first().click();
      await page.waitForTimeout(2000);
  }

    async navigateToTab(page: Page, tabName: string): Promise<void> {
    await page.locator(`//span[text()="${tabName}" and @class='content-tab-title']`).isVisible();
    await page.locator(`//span[text()="${tabName}" and @class='content-tab-title']`).click();
    await page.waitForTimeout(2000);
    console.log(`Successfully navigated to the tab: ${tabName}`);
  }

    async addNat64Prefix(
    page: Page,
    prefixText: string,
    natForDns: string,
    range: string
  ): Promise<void> {
    await page.locator('#page-content').getByText('Add NAT64 Prefix').click();
    await page.getByRole('textbox', { name: 'Enter Text' }).click();
    await page.getByRole('textbox', { name: 'Enter Text' }).fill(prefixText); 
    await page.getByRole('textbox', { name: 'NAT for DNS' }).click();
    await page.getByRole('textbox', { name: 'NAT for DNS' }).fill(natForDns); 
    await page.getByText('Select').click();
    await page.getByText(range).click(); 
     await page.getByText('Save').nth(1).click();
    await page.waitForTimeout(3000);
  }

  async searchNat64Prefix(page: Page, prefixText: string): Promise<void> {
    await page.getByRole('textbox', { name: 'Search...' }).click();
    await page.getByRole('textbox', { name: 'Search...' }).fill(prefixText);
    await page.getByRole('textbox', { name: 'Search...' }).press('Enter');
    await page.waitForTimeout(2000);
  }

  async editNat64Prefix(
    page: Page,
    editedPrefixText: string,
    natForDns: string,
    description: string
  ): Promise<void> {
    await page.getByTitle('Edit').click();
    await page.getByRole('textbox', { name: 'Enter Text' }).click();
    await page.getByRole('textbox', { name: 'Enter Text' }).fill(editedPrefixText);
    await page.getByRole('textbox', { name: 'NAT for DNS' }).click();
    await page.getByRole('textbox', { name: 'NAT for DNS' }).fill(natForDns); 
    await page.locator('textarea').click();
    await page.locator('textarea').fill(description); 
    await page.getByText('Save').nth(1).click();
    await page.waitForTimeout(3000);
  }

  async deleteNat64Prefix(page: Page, prefixText: string): Promise<void> {
    await page.getByTitle('Edit').click();
    await page.getByText('Delete').click();
    await page.getByText('Confirm', { exact: true }).click();
    await this.searchNat64Prefix(page, prefixText);
    const noMatchingItemsLocator = await page.getByText('No matching items found');
    expect(noMatchingItemsLocator.first()).toBeTruthy(); 
  }

  async addDns64Prefix(page: Page, prefixName: string, description: string): Promise<void> {
    await page.getByText('Add DNS64 Prefix').click();
    await page.locator('#page-content').getByText('None').click();
    await page.getByText(prefixName).click();
    await page.locator('textarea').click();
    await page.locator('textarea').fill(description);
    await page.getByText('Save').nth(1).click();
    console.log(`DNS64 Prefix with name '${prefixName}' added successfully.`);
  }
  
  async searchDns64Prefix(page: Page, searchText: string): Promise<void> {
    await page.getByRole('textbox', { name: 'Search...' }).click();
    await page.getByRole('textbox', { name: 'Search...' }).fill(searchText);
    await page.getByRole('textbox', { name: 'Search...' }).press('Enter');
    console.log(`Search performed for: ${searchText}`);
  }

  async clickButton(page: Page, button: string): Promise<void>{
        await page.waitForTimeout(6000);
        const value = await page.locator(this.fields.spanHeadings(button, Selectors[button]));
        expect(value).toHaveText(button);
        await page.locator(this.fields.spanHeadings(button, Selectors[button])).click();
        if(await page.getByText('Confirm').isVisible())
          {
            await page.getByText('OK', { exact: true }).click();
          }
        await page.waitForTimeout(5000);  
        await page.locator("//span[contains(text(),'All changes have been')]").waitFor();
        const messgae = await page.locator("//span[contains(text(),'All changes have been')]");
        expect(messgae).toContainText('All changes have been');
    }
}

export default new Ipv6Configuration();
