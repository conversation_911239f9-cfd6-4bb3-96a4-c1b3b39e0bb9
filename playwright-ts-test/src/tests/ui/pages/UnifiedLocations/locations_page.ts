import { expect, Page } from '@playwright/test';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';
class Locations {
    menuIds:  { [key: string]: string };
    subMenuIds: { [key: string]: string };
    tabs: { [key: string]: string };
    data: { [key: string]: string };
    locationGroups: { [key: string]: string };
    fields: {
        searchTab: string;
        tableData: string;
        connectionTypes: string;
        sublocations: string;
        type: string;
      };
    constructor() {
        this.menuIds = {
            Infrastructure: "nav-pills-tab-3",
        };
        this.subMenuIds = {
            Locations: "mm-tabs-tab-2",
        }
        this.tabs = {
            Edge: "location-table-segment-control-1",
            Cloud: "location-table-segment-control-2",
        };
        this.fields = {
            searchTab: "location-table-search-bar-input",
            tableData: "unified-locations-tooltip",
            type: "z-data-table-row-location-list-type-1",
            connectionTypes: "label-badge",
            sublocations: "z-data-table-row-location-list-subLocationCount-4",
        };
        this.data = {
            "UL_Test_Automation": "location-overview-location-name",
            "IPSec/GRE": "unified-locations-ipsec-gre",
            Sublocations: "sub-location"
        };
        this.locationGroups = {
            "Exclude from Manual Location Groups": "drawer-checkbox-exclude-manual-location",
            "Exclude from Dynamic Location Groups": "drawer-checkbox-exclude-dynamic-location",
        };
    }
    async locations(page: Page): Promise<void> {
        await page.waitForTimeout(6000);
        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Infrastructure'], 'Infrastructure');
        await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds["Locations"], "Locations");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Locations", "mega-menu-columns-group-0-link-0", `(//div[@data-testid="locations-title"])`);
    }
    async searchLocations(page: Page, s: string): Promise<void>{
          await page.waitForTimeout(4000);
          await page.getByTestId(this.fields.searchTab).click();
          await page.getByTestId(this.fields.searchTab).fill(s);
          await page.waitForTimeout(4000);
          await page.locator(`//div[@data-testid='${this.fields.tableData}']/..//span[contains(text(),'${s}')]`).isVisible();
    }
    async verifyLocationsData(page:Page): Promise<void>{
          const name = await page.locator(`//div[@data-testid='${this.fields.tableData}']/..//span[contains(text(),'UL_Test_Automation')]`);
          expect(name).toHaveText('UL_Test_Automation');
          const type = await page.locator(`//div[@data-testid='${this.fields.type}']/..//span[contains(text(),'Edge')]`);
          expect(type).toHaveText('Edge');
          const connectionTypes = await page.getByTestId(this.fields.connectionTypes);
          expect(connectionTypes).toHaveText('IPSec/GRE');
          const sublocations = await page.getByTestId(this.fields.sublocations);
          expect(sublocations).toHaveText('2');
    }

    async toggleTab(page: Page, tab: string): Promise<void>{
          await PlaywrightActions.verifyTextAndClick(page, this.tabs[tab], tab);
    }

    async verifyNoResultFound(page: Page): Promise<void>{
          await page.waitForTimeout(6000);
          const noResultFound = await page.locator("//h5[text()='No Results Found']");
          expect(noResultFound).toHaveText('No Results Found');
    }

    async clickName(page: Page, name: string): Promise<void>{
          await page.locator(`//div[@data-testid='${this.fields.tableData}']/..//span[contains(text(),'${name}')]`).isVisible();
          await page.locator(`//div[@data-testid='${this.fields.tableData}']/..//span[contains(text(),'${name}')]`).click();
          await page.waitForTimeout(6000);
    }

    async verifyTabs(page: Page, verticalTab: string, data: string): Promise<void>{
          const overview = await page.locator(`//button[@data-testid='vertical-tabs-tab' and text()='${verticalTab}']`);
          expect(overview).toContainText(verticalTab);
          const locationData = await page.locator(`//div[@data-testid='${this.data[data]}']/..//div[contains(text(),'${data}')]`);
          expect(locationData).toHaveText(data);
    }

    async clickConnectionType(page: Page): Promise<void>{
          await page.locator(`//button[@data-testid='${this.fields.connectionTypes}' and text()='IPSec/GRE']`).isVisible();
          await page.locator(`//button[@data-testid='${this.fields.connectionTypes}' and text()='IPSec/GRE']`).click();          
          await page.waitForTimeout(6000);
    }

    async clickSublocations(page: Page): Promise<void>{
          await page.locator("//a[text()='2']").isVisible();
          await page.locator("//a[text()='2']").click();
          await page.waitForTimeout(6000);
    }

    async clickAddLocation(page: Page, add: string): Promise<void>{
        await page.waitForTimeout(3000);
        await page.locator("//span[text()='Add']").isVisible();
        await page.locator("//span[text()='Add']").click();
        await page.locator(`//span[text()='${add}']`).isVisible();
        await page.locator(`//span[text()='${add}']`).click();
        await page.waitForTimeout(3000);
    }

    async fillMandatoryValues(page: Page, name: string): Promise<void>{
        await page.getByTestId('form-input-name').click();
        await page.getByTestId('form-input-name').fill(name);
        await page.getByTestId('form-input-cityState').click();
        await page.getByTestId('form-input-cityState').fill('New Delhi');
        await page.getByTestId('drawer-select-country').click();
        await page.getByTestId('basic-search-input').click();
        await page.getByTestId('basic-search-input').fill('India');
        await page.getByTestId('zselect-container-searchable-list-filtered-z-list-list-item-item-1').click();
        await page.getByTestId('drawer-select-timezone').click();
        await page.getByTestId('basic-search-input').click();
        await page.getByTestId('basic-search-input').fill('Kolk');
        await page.getByText('Asia/Kolkata').click();
        await page.getByTestId('input-list-textarea-description').click();
        await page.getByTestId('input-list-textarea-description').fill('Testing');
    }
    async checkLocationGroups(page: Page, locationGroup: string): Promise<void>{
        const locationGroups = await page.getByTestId(this.locationGroups[locationGroup]);
        expect(locationGroups).toHaveText(locationGroup);
        await page.getByTestId(this.locationGroups[locationGroup]).click();
    }
    async selectTrafficType(page: Page, trafficType: string): Promise<void>{
        await page.getByTestId('drawer-select-traffic-type').click();
        await page.getByText(trafficType).click();
        await page.getByTestId('drawer-select-staticIPAddresses').click();
        await page.getByTestId('zselect-container-searchable-list-z-list-list-item-item-0').click();
        await page.getByTestId('drawer-select-staticIPAddresses').click();
        await page.getByTestId('drawer-select-proxyPorts').click();
        await page.getByTestId('drawer-select-proxyPorts').click();
        await page.getByTestId('drawer-select-virtualZENs').click();
        await page.getByText('Virtual Service Edge Clusters').click();
    }
    async verifyLocation(page: Page, name: string): Promise<void>{
        await this.clickSyncLocation(page);
        await page.waitForTimeout(5000);
        await page.getByTestId('alert-1').getByText('Location has been').isVisible();
        await page.getByTestId('location-table-search-bar-input').isVisible();
        await page.getByTestId('location-table-search-bar-input').clear();
        await page.getByTestId('location-table-search-bar-input').click();
        await page.getByTestId('location-table-search-bar-input').fill(name);
        await page.waitForTimeout(5000);
        const names = await page.getByRole('link', { name: name });
        expect(names).toHaveText(name);
        const city = await page.getByText('New Delhi');
        expect(city).toHaveText('New Delhi');
        const country = await page.getByText('India');
        expect(country).toHaveText('India');
    }
    async deleteLocation(page: Page): Promise<void>{
        await page.getByTestId('item-btn0-z-menu-0').isVisible();
        await page.getByTestId('item-btn0-z-menu-0').click();
        await page.getByText('Confirm Delete').isVisible();
        await page.locator("//span[text()='Delete']").click();
        await page.locator("//div[text()='Location has been successfully removed from the system.']").isVisible({timeout: 7000});
        await page.waitForTimeout(2000);
        await this.clickSyncLocation(page);
        await page.waitForTimeout(10000);
        await this.verifyNoResultFound(page);
    }
    async clickAddButton(page: Page): Promise<void>{
        await page.waitForTimeout(3000);
        await page.getByTestId('location-drawer-footer-add').click();
    }
    async verifyRedirectionToBC(page: Page): Promise<void>{
        await page.waitForTimeout(8000);
        const branchProvisioning = await page.locator("//div[text()='Branch Provisioning']");
        expect(branchProvisioning).toHaveText('Branch Provisioning');
        const expectedUrl = 'https://pre-dev.console.zscaler.com/ec/administration/branch-provisioning-templates?filter=BC';
        await page.waitForURL(expectedUrl);
    }
    async verifyRedirectionToCC(page: Page): Promise<void>{
        await page.waitForTimeout(8000);
        const branchProvisioning = await page.locator("//div[text()='Provisioning']");
        expect(branchProvisioning).toHaveText('Provisioning');
        const expectedUrl = 'https://pre-dev.console.zscaler.com/ec/administration/provisioning-templates?filter=CC';
        await page.waitForURL(expectedUrl);
    }
    async verifyPagination(page: Page, row: string): Promise<void>{
        const rows = await page.getByText('Rows per page:');
        expect(rows).toHaveText('Rows per page:');
        await page.getByTestId('location-list-z-table-pagination-page-summary').isVisible();
        await page.getByTestId('location-list-z-table-pagination-zselect-container').click();
        await page.waitForTimeout(2000);
        await page.locator(`//span[contains(text(), "${row} rows")]`).click();
        await page.waitForTimeout(2000);
        const pages = await page.getByTestId('location-list-z-table-pagination-zselect-container');
        expect(pages).toHaveText(row);
    }
    async clickSyncLocation(page: Page): Promise<void>{
        const syncLocation = await page.getByTestId('add-button-group-sync-location');
        expect(syncLocation).toHaveText('Sync Locations');
        await page.getByTestId('add-button-group-sync-location').click();
        await page.getByTestId('add-button-group-sync-location').isDisabled();
    }
    async verifyDuplicateMessage(page: Page): Promise<void>{
        const alert = await page.getByTestId('alert-1').getByText('Location [ id : 118871,');
        expect(alert).toContainText('already exists.');
        await page.getByTestId('alert-1').getByText('Location [ id : 118871,').click();
        await page.getByTestId('unified-locations-location-drawer-close-icon').click();
    }
    async verifyDetails(page: Page): Promise<void>{
        const state = await page.getByText('City/State/ProvinceNew Delhi');
        expect(state).toContainText('New Delhi');
        const country = await page.getByText('CountryIndia');
        expect(country).toContainText('India');
        const timeZone = await page.getByText('Time ZoneAsia/Kolkata');
        expect(timeZone).toContainText('Asia/Kolkata');
        const trafficType = await page.getByText('Traffic TypeCorporate user');
        expect(trafficType).toContainText('Corporate user');
        const location = await page.getByText('Manual Location GroupsNone');
        expect(location).toContainText('None');
    }
    
    async editFunctionality(page: Page): Promise<void>{
        await page.getByTestId('location-overview-edit').click();
        await page.getByTestId('form-input-name').click();
        await page.getByTestId('form-input-name').fill('Location Edit');
        await page.getByTestId('form-input-cityState').click();
        await page.getByTestId('form-input-cityState').fill('Bangalore');
        await page.getByRole('button', { name: 'Asia/Kolkata ' }).click();
        await page.getByTestId('basic-search-input').click();
        await page.getByTestId('basic-search-input').fill('Asia');
        await page.getByText('Asia/Bahrain').click();
        await page.getByTestId('input-list-textarea-description').click();
        await page.getByTestId('input-list-textarea-description').fill('Testing - edit');
        await page.getByRole('checkbox', { name: 'Exclude from Manual Location' }).uncheck();
        await page.getByRole('button', { name: 'None ' }).click();
        await page.getByTestId('zselect-container-searchable-list-z-list-list-item-item-0').getByTestId('checkbox-UNCHECKED').click();
        await page.getByText('Exclude from Manual Location GroupsManual Location Groups admin-').click();
        await page.getByRole('button', { name: 'Corporate user traffic ' }).click();
        await page.getByText('Guest Wi-Fi traffic').click();
    }
    async verifySaveChanges(page: Page): Promise<void>{
        await page.getByTestId('location-drawer-footer-save').click();
        await page.waitForTimeout(8000);
        const header = await page.getByTestId('location-overview').getByText('Location Edit');
        expect(header).toHaveText('Location Edit');
        const description = await page.getByText('Testing- edit');
        expect(description).toHaveText('Testing- edit');
        const state = await page.getByText('City/State/ProvinceBangalore');
        expect(state).toContainText('Bangalore');
        const timeZone = await page.getByText('Time ZoneAsia/Bahrain');
        expect(timeZone).toContainText('Asia/Bahrain');
        const trafficType = await page.getByText('Traffic TypeServer traffic');
        expect(trafficType).toContainText('Server traffic');
        await page.getByRole('link', { name: 'Locations' }).click();
    }
    async editIPSecFunctionality(page: Page): Promise<void>{
        const staticIP = await page.getByText('Static IPs & GRE***********');
        expect(staticIP).toContainText('***********');
        await page.getByTestId('location-ipsec-edit').click();
        await page.getByTestId('drawer-select-staticIPAddresses').click();
        await page.getByTestId('checkbox-CHECKED').click();
        await page.getByTestId('zselect-container-searchable-list-z-list-list-item-item-3').getByTestId('checkbox-UNCHECKED').click();
        await page.getByTestId('drawer-select-virtualZENClusters').getByText('Virtual Service Edge Clusters').click();
        await page.getByTestId('drawer-select-credentialsVPN').click();
        await page.getByTestId('zselect-container-searchable-list-z-list-list-item-item-0').getByTestId('checkbox-UNCHECKED').click();
        await page.getByText('VPN Credentials (Optional)').click();
        await page.getByTestId('drawer-select-virtualZENs').click();
        await page.getByTestId('checkbox-UNCHECKED').click();
        await page.getByTestId('ipsec-gre-drawer').click();
        await page.getByTestId('drawer-select-virtualZENClusters').click();
        await page.getByTestId('checkbox-UNCHECKED').click();
        await page.getByTestId('ipsec-gre-drawer-footer-save').click();
    }

    async verifyIPSecDetails(page: Page): Promise<void>{
        await page.waitForTimeout(7000);
        const vpnCredentials = await page.getByText('VPN Credentials***********');
        expect(vpnCredentials).toContainText('***********');

        const virtualZens = await page.getByText('Virtual ZENstest-all-1');
        expect(virtualZens).toContainText('test-all-1');

        const virtualZenClusters = await page.getByText('Virtual ZEN Clustersvzen-test');
        expect(virtualZenClusters).toContainText('vzen-test');

        await page.getByText('GRE Tunnel Info').isVisible();
        const number = await page.getByTestId('serialNumber-header-cell');
        expect(number).toHaveText('No.');

        const sourceIP = await page.getByTestId('sourceIP-header-cell');
        expect(sourceIP).toHaveText('Tunnel Source IP');

        const destinationIP = await page.getByTestId('destinationIP-header-cell');
        expect(destinationIP).toHaveText('Destination IP');

        const range = await page.getByTestId('destinationRange-header-cell');
        expect(range).toHaveText('Destination Internal Range');
        await page.getByRole('link', { name: 'Locations' }).click();
    }

    async switchTabs(page: Page, tab: string): Promise<void>{
        await page.locator(`//button[text()='${tab}']`).click();
        const verticalTab = await page.locator(`//button[text()='${tab}']`);
        expect(verticalTab).toHaveText(tab);
    }

    async verifyAppliancesEmptyState(page: Page): Promise<void>{
        const noAppliances = await page.getByText('No Appliances');
        expect(noAppliances).toHaveText('No Appliances');

        await page.getByTestId('no-data-card-description').isVisible();
        const addAppliance = await page.getByTestId('no-data-card-button');
        expect(addAppliance).toHaveText('Add Appliance');
    }

    async editConnectionOptionsFunctionality(page: Page): Promise<void>{
        await page.getByRole('button', { name: ' Connection Options' }).click();
        await page.getByLabel('Connection Options').click();
        await page.getByText('AuthenticationDisabled').click();
        await page.getByText('Caution WarningDisabled').click();
        await page.getByText('AUP WarningDisabled').click();
        await page.getByText('Firewall ControlDisabled').click();
        await page.getByText('IPS ControlDisabled').click();
        await page.getByText('XFF ForwardingDisabled').click();
        await page.getByText('Bandwidth ControlDisabled').click();
        await page.getByText('Download (Mbps)0').click();
        await page.getByText('Upload (Mbps)0').click();
        await page.locator('#location-connection-options-edit-button').click();
        await page.getByTestId('authentication-z-switch').locator('span').first().click();
        await page.getByRole('checkbox', { name: 'Enable Kerberos Authentication' }).check();
        await page.getByTestId('drawer-checkbox-ipSurrogate').locator('label').click();
        await page.getByTestId('form-input-unmapUsers').click();
        await page.getByTestId('form-input-unmapUsers').fill('10');
        await page.getByRole('button', { name: 'hours ' }).click();
        await page.getByTestId('zselect-container-z-list-list-item-item-0').click();
        await page.getByTestId('drawer-checkbox-useIpSurrogate').locator('label').click();
        await page.getByTestId('form-input-revalidation').click();
        await page.getByTestId('form-input-revalidation').fill('2');
        await page.getByRole('button', { name: 'hours ' }).click();
        await page.getByTestId('zselect-container-z-list-list-item-item-2').click();
        await page.getByRole('button', { name: 'Cookie ' }).click();
        await page.getByTestId('zselect-container-z-list-list-item-item-1').click();
        await page.getByTestId('enforce-firewall-control-z-switch').locator('span').first().click();
        await page.getByTestId('drawer-checkbox-enable-ips-control').locator('label').click();
        await page.getByTestId('xff-forwarding-z-switch').locator('span').first().click();
        await page.getByTestId('bandwidth-control-z-switch').locator('span').first().click();
        await page.getByTestId('form-input-download-speed').click();
        await page.getByTestId('form-input-download-speed').fill('80');
        await page.getByTestId('form-input-upload-speed').click();
        await page.getByTestId('form-input-upload-speed').fill('50');
        await page.getByTestId('connection-options-drawer-footer-save').click();
        await page.getByText('AuthenticationEnabled').click();
        await page.getByText('Authentication TypesKerberos').click();
        await page.getByText('IP SurrogateEnabled').click();
        await page.getByText('Firewall ControlEnabled').click();
        await page.getByText('IPS ControlEnabled').click();
        await page.getByText('XFF ForwardingEnabled').click();
        await page.getByText('Bandwidth ControlEnabled').click();
        await page.getByText('Download (Mbps)80').click();
        await page.getByText('Upload (Mbps)50').click();
    }

    async verifyLocationExists(page: Page, name: string): Promise<void> {
        await page.waitForTimeout(8000);
        await page.getByTestId('location-table-search-bar-input').isVisible();
        await page.getByTestId('location-table-search-bar-input').click();
        await page.getByTestId('location-table-search-bar-input').fill(name);
        await page.waitForTimeout(5000);
        const noResultFound = await page.locator("//h5[text()='No Results Found']");
        if(await noResultFound.isVisible()) {
            expect(noResultFound).toContainText("No Results Found");
            await page.getByTestId('location-table-search-bar-input').clear();
        } else {
            const names = await page.getByRole('link', { name: name });
            expect(names).toHaveText(name);
            await this.deleteLocation(page);
        }
    }
}
export default new Locations();