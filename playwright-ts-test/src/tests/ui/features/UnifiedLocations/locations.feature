@UnifiedLocations
Feature: Verify the unified locations functionality
# XC-7134
  Scenario: To verify the search functionality in the locations
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User search "UL_Test_Automation" in the search bar
    Then Verify the details in the locations
# XC-7089
  Scenario: To verify search results according to type of locations
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User search "UL_Test_Automation" in the search bar
    And Clicks on the "Edge" tab
    And Verify the details in the locations
    Then Clicks on the "Cloud" tab
    And Verify the No result found message
# XC-8137
  Scenario: To verify the Overview in locations
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User search "UL_Test_Automation" in the search bar
    And Clicks on the name data "UL_Test_Automation"
    Then Verify the "Overview" with data "UL_Test_Automation" of the locations
# XC-7099 XC-7096
  Scenario: To verify the Connection Types in locations
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User search "UL_Test_Automation" in the search bar
    And Clicks on the connection type data
    Then Verify the "IPSec/GRE" with data "IPSec/GRE" of the locations
# XC-8137
  Scenario: To verify the Sublocations in locations
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User search "UL_Test_Automation" in the search bar
    And Clicks on the Sublocations data
    Then Verify the "Sublocations" with data "Sublocations" of the locations
# XC-7150 XC-7865 XC-8042
  Scenario: To verify the Add IPSec Location functionality with traffic type as Corporate user traffic
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    Then Verify if location "IPSec Location Corporate user traffic" already exists
    And User clicks on the "Add IPSec/GRE Location" button
    And User enter the mandatory values with name "IPSec Location Corporate user traffic"
    And User select the "Exclude from Manual Location Groups" checkbox
    Then User fill in the traffic type as "Corporate user traffic" and other details
    Then Clicks on the Add button
    Then Verify the IPSec created "IPSec Location Corporate user traffic"
    And Delete the location
# XC-8047 XC-7866 XC-8040
  Scenario: To verify the Add IPSec Location functionality with traffic type as Guest Wi-Fi traffic
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    Then Verify if location "IPSec Location Guest Wi-Fi traffic" already exists
    And User clicks on the "Add IPSec/GRE Location" button
    And User enter the mandatory values with name "IPSec Location Guest Wi-Fi traffic"
    And User select the "Exclude from Dynamic Location Groups" checkbox
    Then User fill in the traffic type as "Guest Wi-Fi traffic" and other details
    Then Clicks on the Add button
    Then Verify the IPSec created "IPSec Location Guest Wi-Fi traffic"
    And Delete the location
# XC-8043 XC-7869
  Scenario: To verify the Add IPSec Location functionality with traffic type as IoT traffic
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    Then Verify if location "IPSec Location IoT traffic" already exists
    And User clicks on the "Add IPSec/GRE Location" button
    And User enter the mandatory values with name "IPSec Location IoT traffic"
    And User select the "Exclude from Manual Location Groups" checkbox
    And User select the "Exclude from Dynamic Location Groups" checkbox
    Then User fill in the traffic type as "IoT traffic" and other details
    Then Clicks on the Add button
    Then Verify the IPSec created "IPSec Location IoT traffic"
    And Delete the location
# XC-8041 XC-8045 XC-7870 XC-809
  Scenario: To verify the Add IPSec Location functionality with traffic type as Server traffic
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    Then Verify if location "IPSec Location Server traffic" already exists
    And User clicks on the "Add IPSec/GRE Location" button
    And User enter the mandatory values with name "IPSec Location Server traffic"
    Then User fill in the traffic type as "Server traffic" and other details
    Then Clicks on the Add button
    Then Verify the IPSec created "IPSec Location Server traffic"
    And Delete the location

  Scenario: To verify Add Edge Location functionality
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User clicks on the "Add Edge Location" button
    Then Verify the user is redirected to the branch provisioning

  Scenario: To verify Add Cloud Location functionality
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User clicks on the "Add Cloud Location" button
    Then Verify the user is redirected to the provisioning

  Scenario: To verify the pagination of locations screen
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    Then Verify the rows per page and select the "100"

  Scenario: To verify the Sync locations button is clickable
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    Then Verify the sync locations button is clickable