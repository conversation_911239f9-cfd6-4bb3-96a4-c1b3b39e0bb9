import { createBdd } from "playwright-bdd";
const { Before, When, Then, Given } = createBdd();
import EdgeDeploymentTemplate from "../../pages/Administration/EdgeDeploymentTemplate/deploymentTemplate_page";
import { Page } from "@playwright/test";
let ztwPage: EdgeDeploymentTemplate;
Before( async ({ page }: { page: Page }) => {
   ztwPage = new EdgeDeploymentTemplate(page);
});
Given("Navigate to Edge ZTW Deployment Template Page", async () => {
    await ztwPage.navigateTo(ztwPage.path);
    await ztwPage.checkPageUrl(ztwPage.path);
});
Then("Validate Edge Deployment Template Page Header", async () => {
  await ztwPage.validatePageHeader(ztwPage.headerTitle);
});
