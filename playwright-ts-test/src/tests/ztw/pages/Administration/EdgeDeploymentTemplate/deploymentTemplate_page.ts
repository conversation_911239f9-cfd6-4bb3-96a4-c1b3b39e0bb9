import { expect, Page } from "@playwright/test";
import { config } from 'dotenv';
import basicTest from "../../baseTest/baseTest";
config();
const url: string = process.env.ONE_UI_BASE_URL ?? "";
class EdgeDeploymentTemplate extends basicTest{
     page: Page;
    constructor(page: Page ) {
        super( page,"/administration/edge-deployment-templates","Deployment Templates" );
         this.page = page
         if(url.includes("console")){
          this.header = '//div[@class="page-title  header-3"]';
        }
        else{
          this.header = '.component-header-cc-group';
        }
    }
}
export default EdgeDeploymentTemplate;
